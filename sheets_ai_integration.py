#!/usr/bin/env python3
"""
Google Sheets AI Integration Script

This script integrates AI capabilities into Google Sheets using both Google's Gemini API 
and OpenRouter API. It allows reading data from sheets, processing with AI models, 
and writing responses back to designated cells.

Author: AI Assistant
Date: 2025-06-27
"""

import os
import json
import logging
import time
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum

import requests
from google.oauth2.credentials import Credentials
from google.oauth2.service_account import Credentials as ServiceAccountCredentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import google.generativeai as genai


class AIProvider(Enum):
    """Supported AI providers"""
    GEMINI = "gemini"
    OPENROUTER = "openrouter"


@dataclass
class AIRequest:
    """Data class for AI request configuration"""
    provider: AIProvider
    model: str
    prompt: str
    temperature: float = 0.7
    max_tokens: int = 1000
    system_prompt: Optional[str] = None


@dataclass
class SheetRange:
    """Data class for Google Sheets range specification"""
    sheet_id: str
    range_name: str
    worksheet_name: str = "Sheet1"


class SheetsAIIntegrator:
    """Main class for Google Sheets AI integration"""
    
    # Google Sheets API scope
    SCOPES = ['https://www.googleapis.com/auth/spreadsheets']
    
    def __init__(self, config_path: str = "config.json"):
        """
        Initialize the SheetsAIIntegrator
        
        Args:
            config_path: Path to configuration file
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logging()
        self.sheets_service = None
        self.gemini_client = None
        
        # Initialize services
        self._initialize_sheets_service()
        self._initialize_ai_services()
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from file or environment variables"""
        config = {}
        
        # Try to load from config file first
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
        
        # Override with environment variables if available
        config.update({
            'gemini_api_key': os.getenv('GEMINI_API_KEY', config.get('gemini_api_key')),
            'openrouter_api_key': os.getenv('OPENROUTER_API_KEY', config.get('openrouter_api_key')),
            'google_credentials_path': os.getenv('GOOGLE_CREDENTIALS_PATH', 
                                               config.get('google_credentials_path', 'credentials.json')),
            'token_path': os.getenv('TOKEN_PATH', config.get('token_path', 'token.json')),
            'service_account_path': os.getenv('SERVICE_ACCOUNT_PATH', 
                                            config.get('service_account_path')),
            'log_level': os.getenv('LOG_LEVEL', config.get('log_level', 'INFO')),
            'rate_limit_delay': float(os.getenv('RATE_LIMIT_DELAY', 
                                              config.get('rate_limit_delay', 1.0))),
            'max_retries': int(os.getenv('MAX_RETRIES', config.get('max_retries', 3)))
        })
        
        return config
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logger = logging.getLogger('SheetsAI')
        logger.setLevel(getattr(logging, self.config.get('log_level', 'INFO')))
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _initialize_sheets_service(self):
        """Initialize Google Sheets API service"""
        try:
            creds = None
            
            # Use service account if available
            if self.config.get('service_account_path') and os.path.exists(self.config['service_account_path']):
                creds = ServiceAccountCredentials.from_service_account_file(
                    self.config['service_account_path'], scopes=self.SCOPES
                )
                self.logger.info("Using service account credentials")
            else:
                # Use OAuth2 flow
                token_path = self.config.get('token_path', 'token.json')
                credentials_path = self.config.get('google_credentials_path', 'credentials.json')
                
                if os.path.exists(token_path):
                    creds = Credentials.from_authorized_user_file(token_path, self.SCOPES)
                
                if not creds or not creds.valid:
                    if creds and creds.expired and creds.refresh_token:
                        creds.refresh(Request())
                    else:
                        if not os.path.exists(credentials_path):
                            raise FileNotFoundError(f"Google credentials file not found: {credentials_path}")
                        
                        flow = InstalledAppFlow.from_client_secrets_file(credentials_path, self.SCOPES)
                        creds = flow.run_local_server(port=0)
                    
                    # Save credentials for next run
                    with open(token_path, 'w') as token:
                        token.write(creds.to_json())
                
                self.logger.info("Using OAuth2 credentials")
            
            self.sheets_service = build('sheets', 'v4', credentials=creds)
            self.logger.info("Google Sheets service initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Google Sheets service: {e}")
            raise
    
    def _initialize_ai_services(self):
        """Initialize AI service clients"""
        # Initialize Gemini
        if self.config.get('gemini_api_key'):
            try:
                genai.configure(api_key=self.config['gemini_api_key'])
                self.gemini_client = genai.GenerativeModel('gemini-pro')
                self.logger.info("Gemini client initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize Gemini client: {e}")
        
        # OpenRouter doesn't need initialization, just API key validation
        if self.config.get('openrouter_api_key'):
            self.logger.info("OpenRouter API key configured")
    
    def read_sheet_data(self, sheet_range: SheetRange) -> List[List[str]]:
        """
        Read data from Google Sheets
        
        Args:
            sheet_range: SheetRange object specifying the range to read
            
        Returns:
            List of rows, where each row is a list of cell values
        """
        try:
            range_name = f"{sheet_range.worksheet_name}!{sheet_range.range_name}"
            result = self.sheets_service.spreadsheets().values().get(
                spreadsheetId=sheet_range.sheet_id,
                range=range_name
            ).execute()
            
            values = result.get('values', [])
            self.logger.info(f"Read {len(values)} rows from {range_name}")
            return values
            
        except HttpError as e:
            self.logger.error(f"Failed to read sheet data: {e}")
            raise
    
    def write_sheet_data(self, sheet_range: SheetRange, values: List[List[str]]):
        """
        Write data to Google Sheets
        
        Args:
            sheet_range: SheetRange object specifying where to write
            values: List of rows to write
        """
        try:
            range_name = f"{sheet_range.worksheet_name}!{sheet_range.range_name}"
            body = {
                'values': values
            }
            
            result = self.sheets_service.spreadsheets().values().update(
                spreadsheetId=sheet_range.sheet_id,
                range=range_name,
                valueInputOption='RAW',
                body=body
            ).execute()
            
            self.logger.info(f"Updated {result.get('updatedCells', 0)} cells in {range_name}")
            
        except HttpError as e:
            self.logger.error(f"Failed to write sheet data: {e}")
            raise

    def _call_gemini_api(self, ai_request: AIRequest) -> str:
        """
        Call Gemini API for text generation

        Args:
            ai_request: AIRequest object with prompt and parameters

        Returns:
            Generated text response
        """
        try:
            if not self.gemini_client:
                raise ValueError("Gemini client not initialized. Check API key.")

            # Prepare the prompt
            full_prompt = ai_request.prompt
            if ai_request.system_prompt:
                full_prompt = f"{ai_request.system_prompt}\n\n{ai_request.prompt}"

            # Configure generation parameters
            generation_config = genai.types.GenerationConfig(
                temperature=ai_request.temperature,
                max_output_tokens=ai_request.max_tokens,
            )

            # Generate response
            response = self.gemini_client.generate_content(
                full_prompt,
                generation_config=generation_config
            )

            if response.text:
                self.logger.info(f"Gemini API call successful, response length: {len(response.text)}")
                return response.text.strip()
            else:
                self.logger.warning("Gemini API returned empty response")
                return ""

        except Exception as e:
            self.logger.error(f"Gemini API call failed: {e}")
            raise

    def _call_openrouter_api(self, ai_request: AIRequest) -> str:
        """
        Call OpenRouter API for text generation

        Args:
            ai_request: AIRequest object with prompt and parameters

        Returns:
            Generated text response
        """
        try:
            if not self.config.get('openrouter_api_key'):
                raise ValueError("OpenRouter API key not configured")

            url = "https://openrouter.ai/api/v1/chat/completions"

            headers = {
                "Authorization": f"Bearer {self.config['openrouter_api_key']}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://github.com/your-repo",  # Optional
                "X-Title": "Sheets AI Integration"  # Optional
            }

            # Prepare messages
            messages = []
            if ai_request.system_prompt:
                messages.append({"role": "system", "content": ai_request.system_prompt})
            messages.append({"role": "user", "content": ai_request.prompt})

            data = {
                "model": ai_request.model,
                "messages": messages,
                "temperature": ai_request.temperature,
                "max_tokens": ai_request.max_tokens
            }

            response = requests.post(url, headers=headers, json=data, timeout=60)
            response.raise_for_status()

            result = response.json()

            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                self.logger.info(f"OpenRouter API call successful, response length: {len(content)}")
                return content.strip()
            else:
                self.logger.warning("OpenRouter API returned no choices")
                return ""

        except requests.exceptions.RequestException as e:
            self.logger.error(f"OpenRouter API request failed: {e}")
            raise
        except Exception as e:
            self.logger.error(f"OpenRouter API call failed: {e}")
            raise

    def process_ai_request(self, ai_request: AIRequest) -> str:
        """
        Process AI request with retry logic and rate limiting

        Args:
            ai_request: AIRequest object

        Returns:
            AI-generated response
        """
        max_retries = self.config.get('max_retries', 3)
        rate_limit_delay = self.config.get('rate_limit_delay', 1.0)

        for attempt in range(max_retries):
            try:
                # Rate limiting
                if attempt > 0:
                    time.sleep(rate_limit_delay * (2 ** attempt))  # Exponential backoff

                if ai_request.provider == AIProvider.GEMINI:
                    return self._call_gemini_api(ai_request)
                elif ai_request.provider == AIProvider.OPENROUTER:
                    return self._call_openrouter_api(ai_request)
                else:
                    raise ValueError(f"Unsupported AI provider: {ai_request.provider}")

            except Exception as e:
                self.logger.warning(f"AI request attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    raise

        return ""
