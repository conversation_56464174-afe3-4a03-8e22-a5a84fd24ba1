# Google Sheets AI Integration - CHAT Function

A simple and powerful Google Apps Script that adds AI capabilities to Google Sheets through a single `CHAT` function. Use AI directly in your spreadsheet cells with support for both Google's Gemini API and OpenRouter API.

## 🚀 Features

- **Simple CHAT Function**: Use AI directly in cells like `=CHAT("your prompt")`
- **Dual AI Provider Support**: Choose between Gemini or OpenRouter APIs
- **Multiple AI Models**: Access GPT-4, Claude 3, Gemini Pro, Llama 2, and more
- **Data Integration**: Automatically include cell data in your prompts
- **Flexible Parameters**: Control AI behavior with temperature, tokens, and model selection
- **Easy Setup**: Simple menu-driven API key configuration

## 📋 Requirements

- Google Sheets access
- API key from at least one provider:
  - [Google AI Studio](https://aistudio.google.com/app/apikey) for Gemini API
  - [OpenRouter](https://openrouter.ai/keys) for OpenRouter API

## 🛠️ Installation

### Simple Setup (1 File Only)

1. Open Google Sheets
2. Go to `Extensions` → `Apps Script`
3. Delete the default `Code.gs` content
4. Copy and paste the contents of `Code.gs` from this repository
5. Save the project with a meaningful name (e.g., "AI Chat")
6. Return to your Google Sheet and refresh the page

## ⚙️ Setup

### 1. Configure API Keys

1. In your Google Sheet, look for the `AI Chat` menu
2. Click `AI Chat` → `Setup API Keys`
3. Choose which API to configure:
   - **Gemini API Key**: Get from [Google AI Studio](https://aistudio.google.com/app/apikey)
   - **OpenRouter API Key**: Get from [OpenRouter Dashboard](https://openrouter.ai/keys)
4. Enter your API key when prompted

### 2. Grant Permissions

When first running the script, you'll need to grant permissions:
- Access to your Google Sheets
- Access to external APIs (Gemini and OpenRouter)

### 3. Test the Function

- Click `AI Chat` → `Test CHAT Function` to verify everything works
- Or try `=CHAT("What is 2+2?")` in any cell

## 📖 Usage

### Basic CHAT Function

Simply use the CHAT function in any cell:

```
=CHAT("What is the capital of France?")
=CHAT("Explain quantum physics in simple terms")
=CHAT("Write a haiku about coding")
```

### CHAT with Data

Include data from other cells:

```
=CHAT("Analyze this data", A1:B10)
=CHAT("Summarize these reviews", A1:A5)
=CHAT("What's the trend in this data?", C1:C20)
```

### Advanced Usage

Control AI provider, model, and parameters:

```
=CHAT("Translate to Spanish", A1, "gemini")
=CHAT("Generate description", A1, "openrouter", "gpt-4")
=CHAT("Creative writing", A1, "gemini", "gemini-pro", 1.2)
=CHAT("Technical analysis", A1:B5, "openrouter", "claude-3-sonnet", 0.3, 2000)
```

## 📝 CHAT Function Parameters

### Full Syntax
```
=CHAT(prompt, [data], [provider], [model], [temperature], [maxTokens])
```

### Parameters
- **prompt** (required): Your question or instruction for the AI
- **data** (optional): Cell reference or range to include in the prompt
- **provider** (optional): "gemini" or "openrouter" (default: "gemini")
- **model** (optional): Specific model name (default: provider's default)
- **temperature** (optional): 0-2, controls creativity (default: 0.1 for maximum precision)
- **maxTokens** (optional): 1-4000, response length limit (default: 10 for single word answers)

## 🤖 Supported Models

### Google Gemini
- **gemini-2.0-flash**: Latest Gemini model, fast and efficient (default)
- **gemini-pro**: Previous generation model for text analysis
- **gemini-pro-vision**: Can process images and visual content

### OpenRouter
- **anthropic/claude-3-haiku**: Fast, concise responses (default)
- **anthropic/claude-3-sonnet**: Excellent for analysis and reasoning
- **openai/gpt-4**: Powerful for complex reasoning
- **openai/gpt-3.5-turbo**: Fast and cost-effective
- **meta-llama/llama-2-70b-chat**: Open-source alternative

## 💡 Example Use Cases

### 1. Simple Questions
```
=CHAT("What is the capital of Japan?")
=CHAT("Explain machine learning in one paragraph")
=CHAT("Write a professional email greeting")
```

### 2. Data Analysis
```
Sample Data in A1:C4:
Month    Sales    Region
Jan      50000    North
Feb      55000    North
Mar      48000    South

Formula: =CHAT("Analyze this sales data and identify trends", A1:C4)
Result: AI analyzes the data and provides insights about sales patterns
```

### 3. Content Generation
```
Sample Data in A2:B2:
Product: Wireless Mouse | Features: Bluetooth, ergonomic, 1200 DPI

Formula: =CHAT("Generate a marketing description", A2:B2)
Result: Professional product description for e-commerce
```

### 4. Text Processing
```
Sample Data in A1:A3:
"Great product, fast shipping!"
"Poor quality, broke after one week"
"Excellent customer service"

Formula: =CHAT("Analyze sentiment for each review", A1:A3)
Result: Sentiment analysis for each customer review
```

### 5. Language Tasks
```
=CHAT("Translate to Spanish", A1, "gemini")
=CHAT("Summarize in bullet points", A1:A10, "openrouter", "gpt-4")
=CHAT("Extract key themes", A1:A5, "gemini", "gemini-2.0-flash", 0.3)
```

### 6. Creative Tasks
```
=CHAT("Write a haiku about", A1, "gemini", "gemini-2.0-flash", 1.5)
=CHAT("Generate 3 alternative headlines", A1, "openrouter", "gpt-3.5-turbo", 1.2)
```

## ⚙️ Advanced Configuration

### Ultra-Short Responses
The CHAT function is optimized for maximum brevity:
- **Default max tokens**: 10 (for single word answers)
- **Default temperature**: 0.1 (for maximum precision)
- **5-word limit**: Responses aim for 5 words or less
- **Single line**: Only one line responses
- **No explanations**: Just the essential answer

### Temperature Control
- **0.0-0.1**: Ultra-precise, minimal responses (default: 0.1)
- **0.2-0.5**: Short but complete answers
- **0.6-2.0**: Longer, more creative responses

### Getting More Detailed Responses
If you need longer explanations, increase maxTokens and temperature:
```
=CHAT("Explain in detail", A1:B10, "gemini", "gemini-2.0-flash", 0.7, 1000)
```

### Getting Ultra-Precise Responses
For maximum precision, use minimal tokens:
```
=CHAT("Yes or no only", A1, "gemini", "gemini-2.0-flash", 0.1, 10)
=CHAT("One word answer", A1, "gemini", "gemini-2.0-flash", 0.1, 5)
```

### System Prompts
Use system prompts to provide consistent context:
```
System: "You are a financial analyst. Always provide numerical data when possible."
User Prompt: "Analyze this quarterly data"
```

### Output Formatting
- Specify exact cell ranges for output (e.g., "D1:D10")
- Leave empty to display results in a dialog
- Multi-line responses automatically split into rows

## 🔧 Troubleshooting

### Common Issues

**API Key Errors**
- Verify keys are correctly entered
- Check API key permissions and quotas
- Ensure sufficient credits/usage limits

**Rate Limiting**
- The add-on includes automatic retry with exponential backoff
- For large datasets, consider batch processing with delays

**No Response Generated**
- Check your prompt clarity
- Verify the model supports your request type
- Try reducing max tokens if hitting limits

**Permission Errors**
- Re-authorize the script in Google Apps Script
- Check that all required scopes are granted

### Error Logging
Check the Google Apps Script logs:
1. Go to Apps Script editor
2. Click "Executions" to view logs
3. Look for error details and timestamps

## 🔒 Security & Privacy

- API keys are stored securely using Google Apps Script's PropertiesService
- Keys are not shared between users or projects
- Data is sent directly to AI providers (Google/OpenRouter)
- No data is stored permanently by the add-on

## 📊 Rate Limits & Costs

### Google Gemini
- Free tier: 60 requests per minute
- Paid tier: Higher limits based on plan

### OpenRouter
- Varies by model
- Pay-per-use pricing
- Check [OpenRouter pricing](https://openrouter.ai/docs#models) for details

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Review Google Apps Script documentation
3. Check API provider status pages
4. Create an issue in this repository

## 📊 Deployment Guide

### Step-by-Step Deployment

1. **Create Google Apps Script Project**
   - Open [Google Apps Script](https://script.google.com)
   - Click "New Project"
   - Rename to "Sheets AI Integration"

2. **Add Code Files**
   - Replace default `Code.gs` with the provided code
   - Create HTML file named `interface` with the provided HTML
   - Save the project

3. **Authorize Script**
   - Click "Run" button to authorize
   - Grant required permissions
   - Return to Google Sheets and refresh

4. **Test Installation**
   - Look for "AI Integration" menu in Google Sheets
   - Click to open the interface
   - Configure API keys in Setup tab

### Troubleshooting Deployment

**Script doesn't appear in menu**
- Refresh the Google Sheet page
- Check that `onOpen()` function ran successfully
- Verify all files are saved in Apps Script

**Permission errors**
- Re-run the authorization process
- Try incognito mode to clear cached permissions

**API calls fail**
- Verify API keys are correctly entered
- Check API key permissions and quotas

## 💡 Usage Examples

### Data Analysis
```
Sample Data:
A1: Month    B1: Sales    C1: Region
A2: Jan      50000        North
A3: Feb      55000        North

Prompt: "Analyze this sales data and identify trends"
Result: AI-generated insights about sales patterns
```

### Content Generation
```
Sample Data:
A1: Product         B1: Features
A2: Wireless Mouse  Bluetooth, ergonomic

Prompt: "Generate marketing descriptions for each product"
Result: Professional product descriptions
```

### Text Processing
```
Sample Data:
A1: Customer Review
A2: Great product, fast shipping!
A3: Poor quality, broke quickly

Prompt: "Categorize sentiment and extract key themes"
Result: Sentiment analysis and theme extraction
```

### Batch Processing Example
```
Task 1: Analyze customer feedback (A1:A10 → D1)
Task 2: Generate product descriptions (A15:B20 → D15)
Task 3: Summarize financial data (A25:C30 → D25)

All processed simultaneously with different AI models
```

## 🔧 Advanced Configuration

### Temperature Control
- **0.0-0.3**: More focused and deterministic responses
- **0.4-0.7**: Balanced creativity and consistency
- **0.8-2.0**: More creative and varied responses

### System Prompts
Provide consistent context across requests:
```
System: "You are a financial analyst. Always provide numerical data."
User: "Analyze this quarterly data"
```

### Output Management
- Specify exact cell ranges (e.g., "D1:D10")
- Leave empty to display in popup dialog
- Multi-line responses automatically split into rows

## 🔒 Security & Privacy

- **API Key Storage**: Encrypted in Google Apps Script's PropertiesService
- **Data Privacy**: Sent directly to AI providers, not stored by add-on
- **User Isolation**: Keys are project-specific and not shared
- **Permissions**: Only requires Sheets access and external API calls

## 📈 Performance & Limits

### Rate Limiting
- Built-in exponential backoff retry logic
- Automatic delays between batch requests
- Respects API provider rate limits

### API Costs
- **Gemini**: Free tier with 60 requests/minute
- **OpenRouter**: Pay-per-use, varies by model
- Monitor usage in provider dashboards

### Best Practices
- Test with small datasets first
- Use appropriate models for task complexity
- Consider costs for large-scale processing
- Save work before large batch operations

## 🔄 Updates & Maintenance

### Updating the Add-on
1. Copy new code from repository
2. Replace existing code in Apps Script
3. Save and test functionality

### Monitoring
- Check Google Apps Script execution logs
- Monitor API usage and costs
- Review error patterns for optimization

## 🆘 Support & Troubleshooting

### Common Issues
- **No menu appears**: Refresh sheet, check authorization
- **API errors**: Verify keys, check quotas
- **Rate limits**: Use batch processing with delays
- **Large datasets**: Process in smaller chunks

### Getting Help
1. Check troubleshooting section above
2. Review Google Apps Script documentation
3. Check AI provider status pages
4. Create issue in repository

---

**Made with ❤️ for Google Sheets power users**

## 📄 File Structure

This project contains only 2 essential files:
- **Code.gs**: Complete Google Apps Script with CHAT function
- **README.md**: This documentation

## 🎯 Quick Start

1. Copy `Code.gs` into Google Apps Script
2. Save and authorize the script
3. Go to your Google Sheet
4. Use `AI Chat` → `Setup API Keys` to configure
5. Start using `=CHAT("your prompt")` in any cell!

## 🔍 Menu Options

- **Setup API Keys**: Configure Gemini and/or OpenRouter API keys
- **Test CHAT Function**: Verify the function works correctly
- **Help**: View usage examples and syntax
