# Google Sheets AI Integration

A powerful Google Apps Script add-on that integrates AI capabilities into Google Sheets using both Google's Gemini API and OpenRouter API. Process your spreadsheet data with advanced AI models for analysis, summarization, content generation, and more.

## 🚀 Features

- **Dual AI Provider Support**: Use Google Gemini or OpenRouter APIs
- **Multiple AI Models**: Access to GPT-4, Claude 3, Gemini Pro, Llama 2, and more
- **Batch Processing**: Process multiple ranges simultaneously
- **Flexible Output**: Write results to specific cells or view in dialogs
- **Advanced Configuration**: Control temperature, max tokens, and system prompts
- **Rate Limiting**: Built-in retry logic and rate limiting
- **User-friendly Interface**: Clean HTML dialogs for easy interaction

## 📋 Requirements

- Google Sheets access
- API key from at least one provider:
  - [Google AI Studio](https://makersuite.google.com/app/apikey) for Gemini API
  - [OpenRouter](https://openrouter.ai/keys) for OpenRouter API

## 🛠️ Installation

### Method 1: Google Apps Script Editor

1. Open Google Sheets
2. Go to `Extensions` → `Apps Script`
3. Delete the default `Code.gs` content
4. Copy and paste the contents of `Code.gs` from this repository
5. Create new HTML files for each interface:
   - `setup.html`
   - `process.html` 
   - `batch.html`
   - `help.html`
6. Copy the respective HTML content from this repository
7. Save the project with a meaningful name
8. Return to your Google Sheet and refresh the page

### Method 2: Import from GitHub

1. In Google Apps Script, go to `Libraries`
2. Add the script ID (if published as a library)
3. Select the latest version and save

## ⚙️ Setup

### 1. Configure API Keys

1. In your Google Sheet, go to `AI Integration` → `Setup API Keys`
2. Enter your API keys:
   - **Gemini API Key**: Get from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - **OpenRouter API Key**: Get from [OpenRouter Dashboard](https://openrouter.ai/keys)
3. Click "Save API Keys"

### 2. Grant Permissions

When first running the add-on, you'll need to grant permissions:
- Access to your Google Sheets
- Access to external APIs (Gemini and OpenRouter)

## 📖 Usage

### Basic Processing

1. **Select Data**: Highlight the cells containing data you want to process
2. **Open Processor**: Go to `AI Integration` → `Process Selected Range`
3. **Configure Request**:
   - Choose AI Provider (Gemini or OpenRouter)
   - Select Model
   - Enter your prompt describing what you want the AI to do
   - Optionally specify output range
4. **Process**: Click "Process with AI"

### Batch Processing

1. Go to `AI Integration` → `Batch Process`
2. Add multiple processing tasks
3. Configure each task with:
   - Input range
   - Output range
   - AI provider and model
   - Custom prompt
4. Click "Process All Tasks"

## 🤖 Supported Models

### Google Gemini
- **gemini-pro**: Best for text analysis and general tasks
- **gemini-pro-vision**: Can process images and visual content

### OpenRouter
- **anthropic/claude-3-sonnet**: Excellent for analysis and reasoning
- **anthropic/claude-3-haiku**: Faster, good for simple tasks
- **openai/gpt-4**: Powerful for complex reasoning
- **openai/gpt-3.5-turbo**: Fast and cost-effective
- **meta-llama/llama-2-70b-chat**: Open-source alternative

## 💡 Example Use Cases

### Data Analysis
```
Prompt: "Analyze this sales data and identify the top 3 trends"
Data: Monthly sales figures
Output: AI-generated insights about sales patterns
```

### Content Generation
```
Prompt: "Generate a product description for each item"
Data: Product names and features
Output: Marketing copy for each product
```

### Text Summarization
```
Prompt: "Summarize each customer review in one sentence"
Data: Customer feedback text
Output: Concise summaries
```

### Data Categorization
```
Prompt: "Categorize these expenses into budget categories"
Data: Expense descriptions and amounts
Output: Category assignments
```

## ⚙️ Advanced Configuration

### Temperature Control
- **0.0-0.3**: More focused and deterministic
- **0.4-0.7**: Balanced creativity and consistency
- **0.8-2.0**: More creative and varied responses

### System Prompts
Use system prompts to provide consistent context:
```
System: "You are a financial analyst. Always provide numerical data when possible."
User Prompt: "Analyze this quarterly data"
```

### Output Formatting
- Specify exact cell ranges for output (e.g., "D1:D10")
- Leave empty to display results in a dialog
- Multi-line responses automatically split into rows

## 🔧 Troubleshooting

### Common Issues

**API Key Errors**
- Verify keys are correctly entered
- Check API key permissions and quotas
- Ensure sufficient credits/usage limits

**Rate Limiting**
- The add-on includes automatic retry with exponential backoff
- For large datasets, consider batch processing with delays

**No Response Generated**
- Check your prompt clarity
- Verify the model supports your request type
- Try reducing max tokens if hitting limits

**Permission Errors**
- Re-authorize the script in Google Apps Script
- Check that all required scopes are granted

### Error Logging
Check the Google Apps Script logs:
1. Go to Apps Script editor
2. Click "Executions" to view logs
3. Look for error details and timestamps

## 🔒 Security & Privacy

- API keys are stored securely using Google Apps Script's PropertiesService
- Keys are not shared between users or projects
- Data is sent directly to AI providers (Google/OpenRouter)
- No data is stored permanently by the add-on

## 📊 Rate Limits & Costs

### Google Gemini
- Free tier: 60 requests per minute
- Paid tier: Higher limits based on plan

### OpenRouter
- Varies by model
- Pay-per-use pricing
- Check [OpenRouter pricing](https://openrouter.ai/docs#models) for details

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Review Google Apps Script documentation
3. Check API provider status pages
4. Create an issue in this repository

## 🔄 Updates

To update the add-on:
1. Copy new code from the repository
2. Paste into your Google Apps Script project
3. Save and refresh your Google Sheet

---

**Made with ❤️ for Google Sheets power users**
