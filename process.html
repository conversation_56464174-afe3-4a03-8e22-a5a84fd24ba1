<!DOCTYPE html>
<html>
<head>
    <base target="_top">
    <meta charset="utf-8">
    <title>AI Processing</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 {
            color: #1a73e8;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        select, input[type="text"], input[type="number"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            resize: vertical;
            min-height: 80px;
        }
        select:focus, input:focus, textarea:focus {
            outline: none;
            border-color: #1a73e8;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }
        .button {
            background-color: #1a73e8;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .button:hover {
            background-color: #1557b0;
        }
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .range-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #1a73e8;
        }
        .advanced-options {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }
        .advanced-options h3 {
            margin-top: 0;
            color: #333;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Process with AI</h2>
        
        <div id="rangeInfo" class="range-info">
            <strong>Selected Range:</strong> <span id="rangeText">Loading...</span><br>
            <strong>Data Size:</strong> <span id="dataSize">Loading...</span>
        </div>

        <form id="processForm">
            <div class="form-group">
                <label for="provider">AI Provider:</label>
                <select id="provider" name="provider" required>
                    <option value="">Select AI Provider</option>
                    <option value="gemini">Google Gemini</option>
                    <option value="openrouter">OpenRouter</option>
                </select>
            </div>

            <div class="form-group">
                <label for="model">Model:</label>
                <select id="model" name="model" required>
                    <option value="">Select a provider first</option>
                </select>
                <div class="help-text">Choose the AI model to use for processing</div>
            </div>

            <div class="form-group">
                <label for="prompt">Prompt:</label>
                <textarea id="prompt" name="prompt" required placeholder="Enter your prompt here. The selected data will be automatically appended."></textarea>
                <div class="help-text">Describe what you want the AI to do with your data</div>
            </div>

            <div class="form-group">
                <label for="outputRange">Output Range (optional):</label>
                <input type="text" id="outputRange" name="outputRange" placeholder="e.g., D1 or D1:D10">
                <div class="help-text">Where to write the AI response. Leave empty to show in dialog.</div>
            </div>

            <div class="advanced-options">
                <h3>Advanced Options</h3>
                
                <div class="form-group">
                    <label for="systemPrompt">System Prompt (optional):</label>
                    <textarea id="systemPrompt" name="systemPrompt" placeholder="System instructions for the AI model"></textarea>
                    <div class="help-text">Additional context or instructions for the AI</div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="temperature">Temperature:</label>
                        <input type="number" id="temperature" name="temperature" min="0" max="2" step="0.1" value="0.7">
                        <div class="help-text">Controls randomness (0-2)</div>
                    </div>
                    <div class="form-group">
                        <label for="maxTokens">Max Tokens:</label>
                        <input type="number" id="maxTokens" name="maxTokens" min="1" max="4000" value="1000">
                        <div class="help-text">Maximum response length</div>
                    </div>
                </div>
            </div>

            <button type="submit" class="button" id="processButton">Process with AI</button>
            <button type="button" class="button" onclick="google.script.host.close()">Cancel</button>
        </form>

        <div id="status" class="status"></div>
    </div>

    <script>
        // Model options for each provider
        const modelOptions = {
            gemini: [
                { value: 'gemini-pro', text: 'Gemini Pro' },
                { value: 'gemini-pro-vision', text: 'Gemini Pro Vision' }
            ],
            openrouter: [
                { value: 'anthropic/claude-3-sonnet', text: 'Claude 3 Sonnet' },
                { value: 'anthropic/claude-3-haiku', text: 'Claude 3 Haiku' },
                { value: 'openai/gpt-4', text: 'GPT-4' },
                { value: 'openai/gpt-3.5-turbo', text: 'GPT-3.5 Turbo' },
                { value: 'meta-llama/llama-2-70b-chat', text: 'Llama 2 70B' }
            ]
        };

        // Load selected range info on page load
        google.script.run
            .withSuccessHandler(updateRangeInfo)
            .withFailureHandler(showError)
            .getSelectedRange();

        function updateRangeInfo(rangeInfo) {
            if (rangeInfo.success) {
                document.getElementById('rangeText').textContent = 
                    `${rangeInfo.sheetName}!${rangeInfo.range}`;
                document.getElementById('dataSize').textContent = 
                    `${rangeInfo.numRows} rows × ${rangeInfo.numCols} columns`;
            } else {
                document.getElementById('rangeText').textContent = 'Error loading range';
                document.getElementById('dataSize').textContent = rangeInfo.message;
            }
        }

        // Handle provider selection change
        document.getElementById('provider').addEventListener('change', function() {
            const provider = this.value;
            const modelSelect = document.getElementById('model');
            
            // Clear existing options
            modelSelect.innerHTML = '<option value="">Select a model</option>';
            
            if (provider && modelOptions[provider]) {
                modelOptions[provider].forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.value;
                    option.textContent = model.text;
                    modelSelect.appendChild(option);
                });
            }
        });

        // Handle form submission
        document.getElementById('processForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const provider = formData.get('provider');
            const model = formData.get('model');
            const prompt = formData.get('prompt');
            const systemPrompt = formData.get('systemPrompt');
            const temperature = parseFloat(formData.get('temperature'));
            const maxTokens = parseInt(formData.get('maxTokens'));
            const outputRange = formData.get('outputRange');

            if (!provider || !model || !prompt) {
                showStatus('Please fill in all required fields.', 'error');
                return;
            }

            // Disable button and show loading
            const processButton = document.getElementById('processButton');
            processButton.disabled = true;
            processButton.textContent = 'Processing...';

            // Process AI request
            google.script.run
                .withSuccessHandler(handleProcessSuccess)
                .withFailureHandler(handleProcessError)
                .processAIRequest(provider, model, prompt, systemPrompt, temperature, maxTokens, outputRange);
        });

        function handleProcessSuccess(result) {
            const processButton = document.getElementById('processButton');
            processButton.disabled = false;
            processButton.textContent = 'Process with AI';

            if (result.success) {
                showStatus(result.message, 'success');
                
                // If no output range specified, show response in dialog
                if (!document.getElementById('outputRange').value) {
                    const responseWindow = window.open('', '_blank', 'width=600,height=400');
                    responseWindow.document.write(`
                        <html>
                        <head><title>AI Response</title></head>
                        <body style="font-family: Arial, sans-serif; padding: 20px;">
                        <h2>AI Response</h2>
                        <pre style="white-space: pre-wrap; background: #f5f5f5; padding: 15px; border-radius: 4px;">${result.response}</pre>
                        </body>
                        </html>
                    `);
                }
            } else {
                showStatus(result.message, 'error');
            }
        }

        function handleProcessError(error) {
            const processButton = document.getElementById('processButton');
            processButton.disabled = false;
            processButton.textContent = 'Process with AI';
            showStatus('Error processing request: ' + error.message, 'error');
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';
            
            // Hide after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }

        function showError(error) {
            showStatus('Error: ' + error.message, 'error');
        }
    </script>
</body>
</html>
