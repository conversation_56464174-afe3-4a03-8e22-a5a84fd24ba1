# Deployment Guide

This guide walks you through deploying the Google Sheets AI Integration add-on.

## 📋 Prerequisites

1. Google account with access to Google Sheets
2. API keys from at least one provider:
   - Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - OpenRouter API key from [OpenRouter](https://openrouter.ai/keys)

## 🚀 Step-by-Step Deployment

### Step 1: Create Google Apps Script Project

1. Open [Google Apps Script](https://script.google.com)
2. Click "New Project"
3. Rename the project to "Sheets AI Integration"

### Step 2: Add the Code Files

#### Main Script File (Code.gs)
1. Delete the default `function myFunction() {}` code
2. Copy the entire contents of `Code.gs` from this repository
3. Paste it into the editor

#### HTML Interface Files
Create each HTML file by clicking the "+" button and selecting "HTML file":

1. **setup.html**
   - Click "+" → "HTML file"
   - Name it "setup"
   - Replace default content with `setup.html` from repository

2. **process.html**
   - Click "+" → "HTML file"
   - Name it "process"
   - Replace default content with `process.html` from repository

3. **batch.html**
   - Click "+" → "HTML file"
   - Name it "batch"
   - Replace default content with `batch.html` from repository

4. **help.html**
   - Click "+" → "HTML file"
   - Name it "help"
   - Replace default content with `help.html` from repository

#### Manifest File (appsscript.json)
1. Click on "appsscript.json" in the file list
2. Replace the content with `appsscript.json` from repository

### Step 3: Save and Test

1. Click "Save" (Ctrl+S)
2. The project should now have these files:
   - Code.gs
   - setup.html
   - process.html
   - batch.html
   - help.html
   - appsscript.json

### Step 4: Authorize the Script

1. Click "Run" button (or select `onOpen` function and run)
2. You'll be prompted to authorize the script
3. Click "Review permissions"
4. Choose your Google account
5. Click "Advanced" → "Go to Sheets AI Integration (unsafe)"
6. Click "Allow"

### Step 5: Test in Google Sheets

1. Open a Google Sheet (new or existing)
2. Refresh the page
3. You should see "AI Integration" in the menu bar
4. Click it to see the menu options:
   - Setup API Keys
   - Process Selected Range
   - Batch Process
   - Help

## 🔧 Configuration

### Setting Up API Keys

1. In your Google Sheet, click "AI Integration" → "Setup API Keys"
2. Enter your API keys:
   - **Gemini API Key**: From Google AI Studio
   - **OpenRouter API Key**: From OpenRouter
3. Click "Save API Keys"
4. Verify the status indicators show "Connected"

### Testing the Integration

1. Add some sample data to your sheet:
   ```
   A1: Product Name    B1: Features
   A2: Laptop         B2: Fast processor, 16GB RAM
   A3: Phone          B3: 5G, dual camera
   A4: Tablet         B4: Touch screen, lightweight
   ```

2. Select the range A1:B4
3. Click "AI Integration" → "Process Selected Range"
4. Configure:
   - Provider: Gemini or OpenRouter
   - Model: gemini-pro or anthropic/claude-3-haiku
   - Prompt: "Generate a marketing description for each product"
   - Output Range: D1
5. Click "Process with AI"

## 🔒 Security Considerations

### API Key Storage
- API keys are stored in Google Apps Script's PropertiesService
- Keys are encrypted and not accessible to other users
- Keys are project-specific and not shared

### Permissions
The script requires these permissions:
- **Spreadsheets**: To read and write data
- **External Requests**: To call AI APIs

### Data Privacy
- Your data is sent to the selected AI provider
- No data is permanently stored by the add-on
- Review AI provider privacy policies

## 🚨 Troubleshooting Deployment

### Common Issues

**Script doesn't appear in menu**
- Refresh the Google Sheet page
- Check that `onOpen()` function ran successfully
- Verify all files are saved in Apps Script

**Permission errors**
- Re-run the authorization process
- Check that all required scopes are in appsscript.json
- Try incognito mode to clear cached permissions

**HTML dialogs don't open**
- Check browser popup blockers
- Verify HTML files are properly named and saved
- Check browser console for JavaScript errors

**API calls fail**
- Verify API keys are correctly entered
- Check API key permissions and quotas
- Test API keys directly with provider documentation

### Debug Mode

Enable debug logging:
1. In Code.gs, add logging statements:
   ```javascript
   console.log('Debug: Function called');
   Logger.log('Debug: Variable value = ' + variable);
   ```

2. View logs in Apps Script:
   - Go to "Executions" tab
   - Click on recent executions to see logs

## 📊 Performance Optimization

### For Large Datasets
- Use batch processing for multiple ranges
- Implement delays between API calls
- Consider chunking very large datasets

### Rate Limiting
- The script includes automatic retry logic
- Exponential backoff prevents API rate limit issues
- Monitor API usage in provider dashboards

## 🔄 Updates and Maintenance

### Updating the Add-on
1. Copy new code from repository
2. Paste into respective files in Apps Script
3. Save all files
4. Test functionality

### Monitoring Usage
- Check Google Apps Script quotas
- Monitor API usage and costs
- Review execution logs for errors

## 📈 Scaling for Teams

### Shared Deployment
1. Deploy as a web app or add-on
2. Share the Google Apps Script project
3. Each user needs their own API keys

### Organization Deployment
1. Consider Google Workspace add-on deployment
2. Centralized API key management
3. Usage monitoring and cost control

## 🆘 Getting Help

If you encounter issues:
1. Check this troubleshooting guide
2. Review the main README.md
3. Check Google Apps Script documentation
4. Create an issue in the repository

---

**Deployment Complete!** 🎉

Your Google Sheets AI Integration should now be ready to use. Start with the help dialog to learn about features and best practices.
