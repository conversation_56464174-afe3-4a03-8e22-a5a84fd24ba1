/**
 * Google Sheets AI Integration using Gemini and OpenRouter APIs
 *
 * This Google Apps Script provides a simple CHAT function for direct use in cells
 *
 * Author: AI Assistant
 * Date: 2025-06-27
 */

// Configuration constants
const CONFIG = {
  GEMINI_API_KEY: PropertiesService.getScriptProperties().getProperty('GEMINI_API_KEY'),
  OPENROUTER_API_KEY: PropertiesService.getScriptProperties().getProperty('OPENROUTER_API_KEY'),
  RATE_LIMIT_DELAY: 1000, // milliseconds
  MAX_RETRIES: 3,
  DEFAULT_TEMPERATURE: 0.3, // Lower for more precise responses
  DEFAULT_MAX_TOKENS: 150, // Very short for precise answers
  MAX_CELL_LENGTH: 50000 // Google Sheets cell limit
};

// AI Provider enum
const AI_PROVIDERS = {
  GEMINI: 'gemini',
  OPENROUTER: 'openrouter'
};

// Default models
const DEFAULT_MODELS = {
  GEMINI: 'gemini-2.0-flash',
  OPENROUTER: 'anthropic/claude-3-haiku' // Faster, more concise responses
};

/**
 * CHAT function for direct use in Google Sheets cells
 *
 * Usage examples:
 * =CHAT("What is the capital of France?")
 * =CHAT("Analyze this data", A1:B10)
 * =CHAT("Summarize", A1:A5, "gemini")
 * =CHAT("Generate description", A1, "openrouter", "gpt-4")
 * =CHAT("Translate to Spanish", A1, "gemini", "gemini-pro", 0.3)
 *
 * @param {string} prompt - The prompt/question for the AI (required)
 * @param {string|range} [data] - Optional data to include (cell reference or range)
 * @param {string} [provider] - Optional AI provider: "gemini" or "openrouter" (default: "gemini")
 * @param {string} [model] - Optional model name (default: provider's default model)
 * @param {number} [temperature] - Optional temperature 0-2 (default: 0.7)
 * @param {number} [maxTokens] - Optional max tokens (default: 1000)
 * @return {string} AI response
 * @customfunction
 */
function CHAT(prompt, data = null, provider = null, model = null, temperature = null, maxTokens = null) {
  try {
    // Validate required parameters
    if (!prompt || typeof prompt !== 'string') {
      return "Error: Prompt is required and must be text";
    }

    // Set defaults
    provider = provider || 'gemini';
    model = model || DEFAULT_MODELS[provider.toUpperCase()];
    temperature = temperature !== undefined ? temperature : CONFIG.DEFAULT_TEMPERATURE;
    maxTokens = maxTokens || CONFIG.DEFAULT_MAX_TOKENS;

    // Validate provider
    if (!AI_PROVIDERS[provider.toUpperCase()]) {
      return "Error: Provider must be 'gemini' or 'openrouter'";
    }

    // Process data if provided
    let fullPrompt = prompt;
    if (data !== undefined && data !== null && data !== '') {
      let dataText = '';

      if (Array.isArray(data)) {
        // Handle range data
        dataText = convertRangeToText(data);
      } else {
        // Handle single cell data
        dataText = String(data);
      }

      if (dataText.trim()) {
        fullPrompt = `${prompt}\n\nData:\n${dataText}`;
      }
    }

    // Add instruction for precise responses
    fullPrompt = `${fullPrompt}\n\nProvide only the most essential information in 1-2 sentences maximum. Be direct and precise.`;

    // Make AI request
    const aiRequest = {
      provider: AI_PROVIDERS[provider.toUpperCase()],
      model: model,
      prompt: fullPrompt,
      temperature: temperature,
      maxTokens: maxTokens
    };

    const response = callAIService(
      aiRequest.provider,
      aiRequest.model,
      aiRequest.prompt,
      "You are a precise assistant. Give only essential facts in the shortest possible way. No explanations unless asked. Maximum 1-2 sentences.", // system prompt
      aiRequest.temperature,
      aiRequest.maxTokens
    );

    // Truncate response if too long for Google Sheets
    return truncateResponse(response);

  } catch (error) {
    Logger.log('CHAT function error: ' + error.toString());
    return "Error: " + error.toString();
  }
}

/**
 * Truncate response to fit in Google Sheets cell
 */
function truncateResponse(response) {
  if (!response) return '';

  // Remove excessive whitespace and newlines
  let cleanResponse = response.trim().replace(/\n\s*\n/g, '\n').replace(/\s+/g, ' ');

  // If response is too long, truncate it
  if (cleanResponse.length > CONFIG.MAX_CELL_LENGTH) {
    cleanResponse = cleanResponse.substring(0, CONFIG.MAX_CELL_LENGTH - 3) + '...';
  }

  // If response has many lines, limit to 3 lines maximum for precision
  const lines = cleanResponse.split('\n');
  if (lines.length > 3) {
    cleanResponse = lines.slice(0, 3).join('\n');
  }

  return cleanResponse;

  } catch (error) {
    Logger.log('CHAT function error: ' + error.toString());
    return "Error: " + error.toString();
  }
}

/**
 * Initialize the add-on and set up menu
 */
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('AI Chat')
    .addItem('Setup API Keys', 'setupApiKeys')
    .addItem('Test CHAT Function', 'testChatFunction')
    .addSeparator()
    .addItem('Help', 'showHelp')
    .addToUi();
}

/**
 * Setup API keys through prompts
 */
function setupApiKeys() {
  const ui = SpreadsheetApp.getUi();

  // Get current status
  const properties = PropertiesService.getScriptProperties();
  const hasGemini = !!properties.getProperty('GEMINI_API_KEY');
  const hasOpenRouter = !!properties.getProperty('OPENROUTER_API_KEY');

  let message = 'Current Status:\n';
  message += `Gemini API: ${hasGemini ? '✓ Configured' : '✗ Not configured'}\n`;
  message += `OpenRouter API: ${hasOpenRouter ? '✓ Configured' : '✗ Not configured'}\n\n`;
  message += 'Choose an option:';

  const result = ui.alert('API Key Setup', message, ui.ButtonSet.YES_NO_CANCEL);

  if (result === ui.Button.YES) {
    // Setup Gemini
    const geminiKey = ui.prompt('Gemini API Key', 'Enter your Gemini API key from https://aistudio.google.com/app/apikey:', ui.ButtonSet.OK_CANCEL);
    if (geminiKey.getSelectedButton() === ui.Button.OK && geminiKey.getResponseText().trim()) {
      properties.setProperty('GEMINI_API_KEY', geminiKey.getResponseText().trim());
      ui.alert('Success', 'Gemini API key saved!', ui.ButtonSet.OK);
    }
  } else if (result === ui.Button.NO) {
    // Setup OpenRouter
    const openrouterKey = ui.prompt('OpenRouter API Key', 'Enter your OpenRouter API key from https://openrouter.ai/keys:', ui.ButtonSet.OK_CANCEL);
    if (openrouterKey.getSelectedButton() === ui.Button.OK && openrouterKey.getResponseText().trim()) {
      properties.setProperty('OPENROUTER_API_KEY', openrouterKey.getResponseText().trim());
      ui.alert('Success', 'OpenRouter API key saved!', ui.ButtonSet.OK);
    }
  }
}

/**
 * Test the CHAT function
 */
function testChatFunction() {
  const ui = SpreadsheetApp.getUi();

  try {
    const testPrompt = "What is 2+2? Answer in one sentence.";
    const response = CHAT(testPrompt);

    ui.alert('CHAT Function Test', `Test prompt: "${testPrompt}"\n\nResponse: ${response}`, ui.ButtonSet.OK);
  } catch (error) {
    ui.alert('Test Failed', 'Error: ' + error.toString(), ui.ButtonSet.OK);
  }
}

/**
 * Show help information
 */
function showHelp() {
  const ui = SpreadsheetApp.getUi();

  const helpText = `CHAT Function Usage:

Basic usage:
=CHAT("What is the capital of France?")

With data from cells:
=CHAT("Analyze this data", A1:B10)

Specify AI provider:
=CHAT("Summarize", A1:A5, "gemini")
=CHAT("Translate", A1, "openrouter")

Full parameters:
=CHAT(prompt, data, provider, model, temperature, maxTokens)

Examples:
=CHAT("What is 2+2?")
=CHAT("Summarize these reviews", A1:A10)
=CHAT("Generate description", A1, "gemini", "gemini-2.0-flash", 0.5)
=CHAT("Translate to Spanish", A1, "openrouter", "gpt-3.5-turbo")

Providers: "gemini" or "openrouter"
Temperature: 0-2 (lower = more focused, default: 0.3)
Max Tokens: 1-4000 (response length limit, default: 150 for precise answers)`;

  ui.alert('CHAT Function Help', helpText, ui.ButtonSet.OK);
}



/**
 * Convert range values to formatted text
 */
function convertRangeToText(values) {
  if (!values || values.length === 0) {
    return '';
  }

  let text = '';
  for (let i = 0; i < values.length; i++) {
    const row = values[i];
    text += row.join('\t') + '\n';
  }
  return text.trim();
}

/**
 * Call AI service based on provider
 */
function callAIService(provider, model, prompt, systemPrompt, temperature, maxTokens) {
  const maxRetries = CONFIG.MAX_RETRIES;
  let lastError;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      if (attempt > 0) {
        Utilities.sleep(CONFIG.RATE_LIMIT_DELAY * Math.pow(2, attempt));
      }

      if (provider === AI_PROVIDERS.GEMINI) {
        return callGeminiAPI(model, prompt, systemPrompt, temperature, maxTokens);
      } else if (provider === AI_PROVIDERS.OPENROUTER) {
        return callOpenRouterAPI(model, prompt, systemPrompt, temperature, maxTokens);
      } else {
        throw new Error('Unsupported AI provider: ' + provider);
      }

    } catch (error) {
      lastError = error;
      Logger.log(`AI request attempt ${attempt + 1} failed: ${error.toString()}`);
      
      if (attempt === maxRetries - 1) {
        throw error;
      }
    }
  }

  throw lastError;
}

/**
 * Call Gemini API
 */
function callGeminiAPI(model, prompt, systemPrompt, temperature, maxTokens) {
  const apiKey = CONFIG.GEMINI_API_KEY;
  if (!apiKey) {
    throw new Error('Gemini API key not configured');
  }

  // Use the correct Gemini API URL format
  const url = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`;

  // Prepare full prompt
  let fullPrompt = prompt;
  if (systemPrompt) {
    fullPrompt = `${systemPrompt}\n\n${prompt}`;
  }

  const payload = {
    contents: [{
      parts: [{
        text: fullPrompt
      }]
    }],
    generationConfig: {
      temperature: temperature || CONFIG.DEFAULT_TEMPERATURE,
      maxOutputTokens: maxTokens || CONFIG.DEFAULT_MAX_TOKENS
    }
  };

  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    payload: JSON.stringify(payload)
  };

  const response = UrlFetchApp.fetch(url, options);
  const responseData = JSON.parse(response.getContentText());

  if (response.getResponseCode() !== 200) {
    throw new Error(`Gemini API error: ${responseData.error?.message || 'Unknown error'}`);
  }

  if (responseData.candidates && responseData.candidates.length > 0) {
    const content = responseData.candidates[0].content;
    if (content && content.parts && content.parts.length > 0) {
      return content.parts[0].text;
    }
  }

  throw new Error('No response generated by Gemini API');
}

/**
 * Call OpenRouter API
 */
function callOpenRouterAPI(model, prompt, systemPrompt, temperature, maxTokens) {
  const apiKey = CONFIG.OPENROUTER_API_KEY;
  if (!apiKey) {
    throw new Error('OpenRouter API key not configured');
  }

  const url = 'https://openrouter.ai/api/v1/chat/completions';

  // Prepare messages
  const messages = [];
  if (systemPrompt) {
    messages.push({ role: 'system', content: systemPrompt });
  }
  messages.push({ role: 'user', content: prompt });

  const payload = {
    model: model,
    messages: messages,
    temperature: temperature || CONFIG.DEFAULT_TEMPERATURE,
    max_tokens: maxTokens || CONFIG.DEFAULT_MAX_TOKENS
  };

  const options = {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': 'https://script.google.com',
      'X-Title': 'Google Sheets AI Integration'
    },
    payload: JSON.stringify(payload)
  };

  const response = UrlFetchApp.fetch(url, options);
  const responseData = JSON.parse(response.getContentText());

  if (response.getResponseCode() !== 200) {
    throw new Error(`OpenRouter API error: ${responseData.error?.message || 'Unknown error'}`);
  }

  if (responseData.choices && responseData.choices.length > 0) {
    return responseData.choices[0].message.content;
  }

  throw new Error('No response generated by OpenRouter API');
}


