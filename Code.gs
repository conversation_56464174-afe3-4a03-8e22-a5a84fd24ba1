/**
 * Google Sheets AI Integration using Gemini and OpenRouter APIs
 * 
 * This Google Apps Script integrates AI capabilities into Google Sheets
 * using both Google's Gemini API and OpenRouter API.
 * 
 * Author: AI Assistant
 * Date: 2025-06-27
 */

// Configuration constants
const CONFIG = {
  GEMINI_API_KEY: PropertiesService.getScriptProperties().getProperty('GEMINI_API_KEY'),
  OPENROUTER_API_KEY: PropertiesService.getScriptProperties().getProperty('OPENROUTER_API_KEY'),
  RATE_LIMIT_DELAY: 1000, // milliseconds
  MAX_RETRIES: 3,
  DEFAULT_TEMPERATURE: 0.7,
  DEFAULT_MAX_TOKENS: 1000
};

// AI Provider enum
const AI_PROVIDERS = {
  GEMINI: 'gemini',
  OPENROUTER: 'openrouter'
};

// Default models
const DEFAULT_MODELS = {
  GEMINI: 'gemini-pro',
  OPENROUTER: 'anthropic/claude-3-sonnet'
};

/**
 * Initialize the add-on and set up menu
 */
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('AI Integration')
    .addItem('Setup API Keys', 'showSetupDialog')
    .addItem('Process Selected Range', 'showProcessDialog')
    .addItem('Batch Process', 'showBatchDialog')
    .addSeparator()
    .addItem('Help', 'showHelpDialog')
    .addToUi();
}

/**
 * Show setup dialog for API keys
 */
function showSetupDialog() {
  const html = HtmlService.createHtmlOutputFromFile('setup')
    .setWidth(500)
    .setHeight(400)
    .setTitle('AI Integration Setup');
  SpreadsheetApp.getUi().showModalDialog(html, 'Setup API Keys');
}

/**
 * Show process dialog for AI requests
 */
function showProcessDialog() {
  const html = HtmlService.createHtmlOutputFromFile('process')
    .setWidth(600)
    .setHeight(500)
    .setTitle('Process with AI');
  SpreadsheetApp.getUi().showModalDialog(html, 'AI Processing');
}

/**
 * Show batch processing dialog
 */
function showBatchDialog() {
  const html = HtmlService.createHtmlOutputFromFile('batch')
    .setWidth(700)
    .setHeight(600)
    .setTitle('Batch AI Processing');
  SpreadsheetApp.getUi().showModalDialog(html, 'Batch Processing');
}

/**
 * Show help dialog
 */
function showHelpDialog() {
  const html = HtmlService.createHtmlOutputFromFile('help')
    .setWidth(600)
    .setHeight(500)
    .setTitle('AI Integration Help');
  SpreadsheetApp.getUi().showModalDialog(html, 'Help');
}

/**
 * Save API keys to script properties
 */
function saveApiKeys(geminiKey, openrouterKey) {
  try {
    const properties = PropertiesService.getScriptProperties();
    if (geminiKey) {
      properties.setProperty('GEMINI_API_KEY', geminiKey);
    }
    if (openrouterKey) {
      properties.setProperty('OPENROUTER_API_KEY', openrouterKey);
    }
    return { success: true, message: 'API keys saved successfully!' };
  } catch (error) {
    Logger.log('Error saving API keys: ' + error.toString());
    return { success: false, message: 'Error saving API keys: ' + error.toString() };
  }
}

/**
 * Get current API key status
 */
function getApiKeyStatus() {
  const properties = PropertiesService.getScriptProperties();
  return {
    hasGemini: !!properties.getProperty('GEMINI_API_KEY'),
    hasOpenRouter: !!properties.getProperty('OPENROUTER_API_KEY')
  };
}

/**
 * Get selected range information
 */
function getSelectedRange() {
  try {
    const sheet = SpreadsheetApp.getActiveSheet();
    const range = sheet.getActiveRange();
    
    return {
      success: true,
      sheetName: sheet.getName(),
      range: range.getA1Notation(),
      numRows: range.getNumRows(),
      numCols: range.getNumColumns(),
      values: range.getValues()
    };
  } catch (error) {
    Logger.log('Error getting selected range: ' + error.toString());
    return { success: false, message: error.toString() };
  }
}

/**
 * Process AI request for selected range
 */
function processAIRequest(provider, model, prompt, systemPrompt, temperature, maxTokens, outputRange) {
  try {
    // Validate inputs
    if (!provider || !model || !prompt) {
      throw new Error('Provider, model, and prompt are required');
    }

    // Get selected range data
    const rangeInfo = getSelectedRange();
    if (!rangeInfo.success) {
      throw new Error('Failed to get selected range: ' + rangeInfo.message);
    }

    // Convert range data to text
    const dataText = convertRangeToText(rangeInfo.values);
    
    // Prepare full prompt
    const fullPrompt = `${prompt}\n\nData to process:\n${dataText}`;

    // Make AI request
    const aiResponse = callAIService(provider, model, fullPrompt, systemPrompt, temperature, maxTokens);

    // Write response to output range
    if (outputRange) {
      writeToRange(outputRange, aiResponse);
    }

    return {
      success: true,
      response: aiResponse,
      message: 'AI processing completed successfully!'
    };

  } catch (error) {
    Logger.log('Error processing AI request: ' + error.toString());
    return { success: false, message: error.toString() };
  }
}

/**
 * Convert range values to formatted text
 */
function convertRangeToText(values) {
  if (!values || values.length === 0) {
    return '';
  }

  let text = '';
  for (let i = 0; i < values.length; i++) {
    const row = values[i];
    text += row.join('\t') + '\n';
  }
  return text.trim();
}

/**
 * Call AI service based on provider
 */
function callAIService(provider, model, prompt, systemPrompt, temperature, maxTokens) {
  const maxRetries = CONFIG.MAX_RETRIES;
  let lastError;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      if (attempt > 0) {
        Utilities.sleep(CONFIG.RATE_LIMIT_DELAY * Math.pow(2, attempt));
      }

      if (provider === AI_PROVIDERS.GEMINI) {
        return callGeminiAPI(model, prompt, systemPrompt, temperature, maxTokens);
      } else if (provider === AI_PROVIDERS.OPENROUTER) {
        return callOpenRouterAPI(model, prompt, systemPrompt, temperature, maxTokens);
      } else {
        throw new Error('Unsupported AI provider: ' + provider);
      }

    } catch (error) {
      lastError = error;
      Logger.log(`AI request attempt ${attempt + 1} failed: ${error.toString()}`);
      
      if (attempt === maxRetries - 1) {
        throw error;
      }
    }
  }

  throw lastError;
}

/**
 * Call Gemini API
 */
function callGeminiAPI(model, prompt, systemPrompt, temperature, maxTokens) {
  const apiKey = CONFIG.GEMINI_API_KEY;
  if (!apiKey) {
    throw new Error('Gemini API key not configured');
  }

  const url = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`;
  
  // Prepare full prompt
  let fullPrompt = prompt;
  if (systemPrompt) {
    fullPrompt = `${systemPrompt}\n\n${prompt}`;
  }

  const payload = {
    contents: [{
      parts: [{
        text: fullPrompt
      }]
    }],
    generationConfig: {
      temperature: temperature || CONFIG.DEFAULT_TEMPERATURE,
      maxOutputTokens: maxTokens || CONFIG.DEFAULT_MAX_TOKENS
    }
  };

  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    payload: JSON.stringify(payload)
  };

  const response = UrlFetchApp.fetch(url, options);
  const responseData = JSON.parse(response.getContentText());

  if (response.getResponseCode() !== 200) {
    throw new Error(`Gemini API error: ${responseData.error?.message || 'Unknown error'}`);
  }

  if (responseData.candidates && responseData.candidates.length > 0) {
    const content = responseData.candidates[0].content;
    if (content && content.parts && content.parts.length > 0) {
      return content.parts[0].text;
    }
  }

  throw new Error('No response generated by Gemini API');
}

/**
 * Call OpenRouter API
 */
function callOpenRouterAPI(model, prompt, systemPrompt, temperature, maxTokens) {
  const apiKey = CONFIG.OPENROUTER_API_KEY;
  if (!apiKey) {
    throw new Error('OpenRouter API key not configured');
  }

  const url = 'https://openrouter.ai/api/v1/chat/completions';

  // Prepare messages
  const messages = [];
  if (systemPrompt) {
    messages.push({ role: 'system', content: systemPrompt });
  }
  messages.push({ role: 'user', content: prompt });

  const payload = {
    model: model,
    messages: messages,
    temperature: temperature || CONFIG.DEFAULT_TEMPERATURE,
    max_tokens: maxTokens || CONFIG.DEFAULT_MAX_TOKENS
  };

  const options = {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': 'https://script.google.com',
      'X-Title': 'Google Sheets AI Integration'
    },
    payload: JSON.stringify(payload)
  };

  const response = UrlFetchApp.fetch(url, options);
  const responseData = JSON.parse(response.getContentText());

  if (response.getResponseCode() !== 200) {
    throw new Error(`OpenRouter API error: ${responseData.error?.message || 'Unknown error'}`);
  }

  if (responseData.choices && responseData.choices.length > 0) {
    return responseData.choices[0].message.content;
  }

  throw new Error('No response generated by OpenRouter API');
}

/**
 * Write AI response to specified range
 */
function writeToRange(rangeNotation, content) {
  try {
    const sheet = SpreadsheetApp.getActiveSheet();
    const range = sheet.getRange(rangeNotation);
    
    // If content is multi-line, split into rows
    const lines = content.split('\n');
    if (lines.length > 1 && range.getNumRows() >= lines.length) {
      const values = lines.map(line => [line]);
      range.getRange(1, 1, lines.length, 1).setValues(values);
    } else {
      range.setValue(content);
    }
    
    return true;
  } catch (error) {
    Logger.log('Error writing to range: ' + error.toString());
    throw error;
  }
}

/**
 * Batch process multiple ranges
 */
function batchProcess(requests) {
  const results = [];
  
  for (let i = 0; i < requests.length; i++) {
    const request = requests[i];
    try {
      // Get data from input range
      const sheet = SpreadsheetApp.getActiveSheet();
      const inputRange = sheet.getRange(request.inputRange);
      const values = inputRange.getValues();
      const dataText = convertRangeToText(values);
      
      // Prepare prompt
      const fullPrompt = `${request.prompt}\n\nData to process:\n${dataText}`;
      
      // Make AI request
      const response = callAIService(
        request.provider,
        request.model,
        fullPrompt,
        request.systemPrompt,
        request.temperature,
        request.maxTokens
      );
      
      // Write to output range
      if (request.outputRange) {
        writeToRange(request.outputRange, response);
      }
      
      results.push({
        success: true,
        inputRange: request.inputRange,
        outputRange: request.outputRange,
        response: response.substring(0, 100) + '...' // Truncate for display
      });
      
      // Rate limiting between requests
      if (i < requests.length - 1) {
        Utilities.sleep(CONFIG.RATE_LIMIT_DELAY);
      }
      
    } catch (error) {
      Logger.log(`Batch process error for range ${request.inputRange}: ${error.toString()}`);
      results.push({
        success: false,
        inputRange: request.inputRange,
        outputRange: request.outputRange,
        error: error.toString()
      });
    }
  }
  
  return results;
}
