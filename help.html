<!DOCTYPE html>
<html>
<head>
    <base target="_top">
    <meta charset="utf-8">
    <title>AI Integration Help</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-height: 500px;
            overflow-y: auto;
        }
        h2 {
            color: #1a73e8;
            margin-bottom: 20px;
        }
        h3 {
            color: #333;
            margin-top: 25px;
            margin-bottom: 10px;
        }
        .section {
            margin-bottom: 25px;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #1a73e8;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .tip {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 5px;
        }
        a {
            color: #1a73e8;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        .button {
            background-color: #1a73e8;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 20px;
        }
        .button:hover {
            background-color: #1557b0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>AI Integration Help</h2>
        
        <div class="section">
            <h3>🚀 Getting Started</h3>
            <p>This add-on integrates AI capabilities into Google Sheets using Gemini and OpenRouter APIs.</p>
            
            <ol>
                <li><strong>Setup API Keys:</strong> Go to AI Integration → Setup API Keys</li>
                <li><strong>Select Data:</strong> Highlight the cells you want to process</li>
                <li><strong>Process:</strong> Use AI Integration → Process Selected Range</li>
            </ol>
        </div>

        <div class="section">
            <h3>🔑 API Key Setup</h3>
            <p>You need API keys from one or both providers:</p>
            
            <ul>
                <li><strong>Gemini API:</strong> Get your key from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></li>
                <li><strong>OpenRouter API:</strong> Get your key from <a href="https://openrouter.ai/keys" target="_blank">OpenRouter Dashboard</a></li>
            </ul>
            
            <div class="warning">
                <strong>Security Note:</strong> API keys are stored securely in Google Apps Script's PropertiesService and are not shared with other users.
            </div>
        </div>

        <div class="section">
            <h3>📊 Processing Data</h3>
            <p>The add-on can process your spreadsheet data in several ways:</p>
            
            <ul>
                <li><strong>Single Range:</strong> Select cells and use "Process Selected Range"</li>
                <li><strong>Batch Processing:</strong> Process multiple ranges at once</li>
                <li><strong>Custom Prompts:</strong> Tell the AI exactly what to do with your data</li>
            </ul>
            
            <div class="tip">
                <strong>Tip:</strong> Your selected data is automatically included in the prompt, so you just need to describe what you want the AI to do with it.
            </div>
        </div>

        <div class="section">
            <h3>🤖 AI Providers & Models</h3>
            
            <h4>Google Gemini</h4>
            <ul>
                <li><strong>Gemini Pro:</strong> Best for text analysis, summarization, and general tasks</li>
                <li><strong>Gemini Pro Vision:</strong> Can process images (if your data contains image URLs)</li>
            </ul>
            
            <h4>OpenRouter</h4>
            <ul>
                <li><strong>Claude 3 Sonnet:</strong> Excellent for analysis and reasoning</li>
                <li><strong>Claude 3 Haiku:</strong> Faster, good for simple tasks</li>
                <li><strong>GPT-4:</strong> Powerful for complex reasoning</li>
                <li><strong>GPT-3.5 Turbo:</strong> Fast and cost-effective</li>
                <li><strong>Llama 2 70B:</strong> Open-source alternative</li>
            </ul>
        </div>

        <div class="section">
            <h3>💡 Example Use Cases</h3>
            
            <div class="code">
                <strong>Data Analysis:</strong><br>
                "Analyze this sales data and provide insights about trends and patterns."
            </div>
            
            <div class="code">
                <strong>Text Summarization:</strong><br>
                "Summarize each of these customer reviews in one sentence."
            </div>
            
            <div class="code">
                <strong>Data Categorization:</strong><br>
                "Categorize these products into appropriate categories."
            </div>
            
            <div class="code">
                <strong>Content Generation:</strong><br>
                "Generate marketing copy for each of these products."
            </div>
        </div>

        <div class="section">
            <h3>⚙️ Advanced Options</h3>
            
            <ul>
                <li><strong>Temperature (0-2):</strong> Controls randomness. Lower = more focused, Higher = more creative</li>
                <li><strong>Max Tokens:</strong> Maximum length of AI response</li>
                <li><strong>System Prompt:</strong> Additional context or instructions for the AI</li>
                <li><strong>Output Range:</strong> Specify where to write the AI response</li>
            </ul>
        </div>

        <div class="section">
            <h3>🔧 Troubleshooting</h3>
            
            <ul>
                <li><strong>API Key Errors:</strong> Check that your API keys are valid and have sufficient credits</li>
                <li><strong>Rate Limits:</strong> The add-on includes automatic retry with exponential backoff</li>
                <li><strong>Large Data:</strong> For very large datasets, consider using batch processing</li>
                <li><strong>No Response:</strong> Check your prompt and ensure the AI provider supports your request</li>
            </ul>
            
            <div class="warning">
                <strong>Rate Limits:</strong> Both APIs have rate limits. The add-on includes delays between requests to avoid hitting these limits.
            </div>
        </div>

        <div class="section">
            <h3>📝 Best Practices</h3>
            
            <ul>
                <li>Be specific in your prompts for better results</li>
                <li>Test with small data sets first</li>
                <li>Use system prompts for consistent formatting</li>
                <li>Consider the cost of API calls for large datasets</li>
                <li>Save your work before processing large batches</li>
            </ul>
        </div>

        <div class="section">
            <h3>🆘 Support</h3>
            <p>If you encounter issues:</p>
            <ul>
                <li>Check the browser console for error messages</li>
                <li>Verify your API keys are correctly configured</li>
                <li>Try with a smaller dataset first</li>
                <li>Check API provider status pages for outages</li>
            </ul>
        </div>

        <button type="button" class="button" onclick="google.script.host.close()">Close</button>
    </div>
</body>
</html>
