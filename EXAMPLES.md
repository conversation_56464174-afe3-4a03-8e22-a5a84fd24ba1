# Usage Examples

This document provides practical examples of how to use the Google Sheets AI Integration for various tasks.

## 📊 Data Analysis Examples

### Sales Data Analysis
**Scenario**: Analyze monthly sales data to identify trends

**Sample Data**:
```
A1: Month      B1: Sales     C1: Region
A2: Jan        50000         North
A3: Feb        55000         North
A4: Mar        48000         North
A5: Jan        45000         South
A6: Feb        52000         South
A7: Mar        49000         South
```

**Configuration**:
- **Range**: A1:C7
- **Provider**: Gemini
- **Model**: gemini-pro
- **Prompt**: "Analyze this sales data and provide insights about trends, regional performance, and recommendations for improvement."
- **Output**: D1

**Expected Output**: Detailed analysis of sales trends, regional comparisons, and actionable recommendations.

### Customer Feedback Analysis
**Scenario**: Categorize and analyze customer reviews

**Sample Data**:
```
A1: Review
A2: Great product, fast shipping!
A3: Poor quality, broke after one week
A4: Excellent customer service
A5: Overpriced but good quality
```

**Configuration**:
- **Range**: A1:A5
- **Provider**: OpenRouter
- **Model**: anthropic/claude-3-sonnet
- **Prompt**: "Analyze these customer reviews. For each review, provide: 1) Sentiment (Positive/Negative/Neutral), 2) Main topic, 3) Key issues or praise points."
- **System Prompt**: "You are a customer experience analyst. Provide structured analysis in a consistent format."

## 📝 Content Generation Examples

### Product Descriptions
**Scenario**: Generate marketing copy for products

**Sample Data**:
```
A1: Product        B1: Features                    C1: Price
A2: Wireless Mouse Bluetooth, ergonomic, 1200 DPI $29.99
A3: USB Keyboard   Mechanical, RGB backlit        $79.99
A4: Monitor        27", 4K, IPS panel             $299.99
```

**Configuration**:
- **Range**: A1:C4
- **Provider**: OpenRouter
- **Model**: openai/gpt-4
- **Prompt**: "Create compelling product descriptions for an e-commerce website. Include key features, benefits, and a call-to-action. Keep each description under 100 words."
- **Output**: D1:D4

### Email Templates
**Scenario**: Generate personalized email templates

**Sample Data**:
```
A1: Customer Name  B1: Product      C1: Issue
A2: John Smith     Laptop          Screen flickering
A3: Sarah Johnson  Phone           Battery draining
A4: Mike Brown     Tablet          Won't turn on
```

**Configuration**:
- **Range**: A1:C4
- **Provider**: Gemini
- **Model**: gemini-pro
- **Prompt**: "Generate professional customer service email responses. Address the customer by name, acknowledge their specific issue, and provide helpful next steps."
- **System Prompt**: "You are a customer service representative. Be empathetic, professional, and solution-focused."

## 🔍 Data Processing Examples

### Text Summarization
**Scenario**: Summarize long text content

**Sample Data**:
```
A1: Article Title                    B1: Full Text (long articles)
A2: "AI in Healthcare"              [Long article text...]
A3: "Climate Change Solutions"       [Long article text...]
A4: "Remote Work Trends"            [Long article text...]
```

**Configuration**:
- **Range**: A1:B4
- **Provider**: OpenRouter
- **Model**: anthropic/claude-3-haiku
- **Prompt**: "Summarize each article in exactly 2-3 sentences. Focus on the main points and key takeaways."
- **Output**: C1:C4

### Data Categorization
**Scenario**: Categorize expenses into budget categories

**Sample Data**:
```
A1: Expense Description    B1: Amount
A2: Office supplies        $150
A3: Team lunch            $85
A4: Software license      $299
A5: Travel expenses       $450
A6: Marketing materials   $200
```

**Configuration**:
- **Range**: A1:B6
- **Provider**: Gemini
- **Model**: gemini-pro
- **Prompt**: "Categorize each expense into one of these categories: Office Supplies, Meals & Entertainment, Software, Travel, Marketing, Other. Provide only the category name."
- **System Prompt**: "You are an accounting assistant. Be consistent with categorization rules."

## 🧮 Formula and Calculation Examples

### Financial Analysis
**Scenario**: Analyze financial ratios and provide insights

**Sample Data**:
```
A1: Company    B1: Revenue   C1: Expenses  D1: Profit
A2: Company A  1000000      800000        200000
A3: Company B  750000       600000        150000
A4: Company C  1200000      1000000       200000
```

**Configuration**:
- **Range**: A1:D4
- **Provider**: OpenRouter
- **Model**: openai/gpt-4
- **Prompt**: "Calculate profit margins for each company and rank them by financial performance. Provide insights about which company is performing best and why."

### Statistical Analysis
**Scenario**: Perform statistical analysis on survey data

**Sample Data**:
```
A1: Question              B1: Score1  C1: Score2  D1: Score3  E1: Score4  F1: Score5
A2: Product Satisfaction  4           5           3           4           5
A3: Service Quality       3           4           4           5           4
A4: Value for Money       2           3           4           3           4
```

**Configuration**:
- **Range**: A1:F4
- **Provider**: Gemini
- **Model**: gemini-pro
- **Prompt**: "Calculate mean, median, and standard deviation for each survey question. Identify which areas score highest and lowest, and provide recommendations for improvement."

## 🌐 Language and Translation Examples

### Multi-language Content
**Scenario**: Translate product names and descriptions

**Sample Data**:
```
A1: English Product Name    B1: English Description
A2: Wireless Headphones    High-quality sound with noise cancellation
A3: Smart Watch            Fitness tracking with heart rate monitor
A4: Bluetooth Speaker      Portable speaker with 12-hour battery
```

**Configuration**:
- **Range**: A1:B4
- **Provider**: OpenRouter
- **Model**: openai/gpt-3.5-turbo
- **Prompt**: "Translate the product names and descriptions to Spanish. Maintain the marketing tone and ensure cultural appropriateness."
- **Output**: C1:D4

### Content Localization
**Scenario**: Adapt marketing messages for different regions

**Sample Data**:
```
A1: Original Message                           B1: Target Region
A2: "Save big on our summer sale!"           Europe
A3: "Free shipping on orders over $50"       Canada
A4: "New year, new deals!"                   Asia
```

**Configuration**:
- **Range**: A1:B4
- **Provider**: Gemini
- **Model**: gemini-pro
- **Prompt**: "Adapt each marketing message for the target region. Consider local customs, currency, seasons, and cultural preferences."

## 🔄 Batch Processing Examples

### Multiple Data Types
**Scenario**: Process different types of data in one batch

**Batch Configuration**:

**Task 1 - Customer Analysis**:
- **Input Range**: A1:B10 (Customer feedback)
- **Output Range**: D1
- **Prompt**: "Analyze customer sentiment and extract key themes"

**Task 2 - Product Descriptions**:
- **Input Range**: A15:C20 (Product data)
- **Output Range**: D15
- **Prompt**: "Generate marketing descriptions for each product"

**Task 3 - Financial Summary**:
- **Input Range**: A25:E30 (Financial data)
- **Output Range**: D25
- **Prompt**: "Summarize financial performance and identify trends"

### Comparative Analysis
**Scenario**: Compare different datasets with consistent analysis

**Sample Setup**:
```
Sheet1: Q1 Data (A1:C10)
Sheet2: Q2 Data (A1:C10)
Sheet3: Q3 Data (A1:C10)
```

**Batch Tasks**:
- Process each quarter's data with the same prompt
- Compare results across quarters
- Generate quarterly insights

## 🎯 Advanced Use Cases

### Sentiment Analysis Pipeline
**Scenario**: Multi-step sentiment analysis

**Step 1**: Extract sentiment scores
- **Prompt**: "Rate sentiment from 1-10 (1=very negative, 10=very positive) and provide the number only"

**Step 2**: Categorize by sentiment
- **Prompt**: "Based on the sentiment scores, categorize as: Positive (8-10), Neutral (4-7), Negative (1-3)"

**Step 3**: Generate action items
- **Prompt**: "For negative feedback, suggest specific action items to address concerns"

### Competitive Analysis
**Scenario**: Analyze competitor data

**Sample Data**:
```
A1: Competitor  B1: Price  C1: Features           D1: Reviews
A2: Company X   $99       Feature A, B, C        4.2/5
A3: Company Y   $129      Feature A, B, C, D     4.5/5
A4: Company Z   $79       Feature A, B           3.8/5
```

**Configuration**:
- **Provider**: OpenRouter
- **Model**: anthropic/claude-3-sonnet
- **Prompt**: "Analyze this competitive landscape. Identify pricing strategies, feature gaps, and market positioning opportunities. Provide strategic recommendations."

## 💡 Tips for Better Results

### Prompt Engineering
1. **Be Specific**: "Summarize in 2 sentences" vs "Summarize"
2. **Provide Context**: Use system prompts for consistent formatting
3. **Include Examples**: Show the desired output format
4. **Set Constraints**: Specify word limits, format requirements

### Data Preparation
1. **Clean Data**: Remove empty rows and inconsistent formatting
2. **Structure Headers**: Use clear, descriptive column headers
3. **Consistent Format**: Ensure data types are consistent within columns

### Output Management
1. **Plan Output Ranges**: Ensure sufficient space for responses
2. **Test Small First**: Try with a few rows before processing large datasets
3. **Save Backups**: Always backup your sheet before large operations

---

These examples should help you get started with various AI-powered spreadsheet tasks. Experiment with different prompts and models to find what works best for your specific use cases!
