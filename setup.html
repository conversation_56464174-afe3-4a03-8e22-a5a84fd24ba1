<!DOCTYPE html>
<html>
<head>
    <base target="_top">
    <meta charset="utf-8">
    <title>AI Integration Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 {
            color: #1a73e8;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #1a73e8;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }
        .button {
            background-color: #1a73e8;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .button:hover {
            background-color: #1557b0;
        }
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .api-status {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.connected {
            background-color: #34a853;
        }
        .status-indicator.disconnected {
            background-color: #ea4335;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>AI Integration Setup</h2>
        
        <div id="currentStatus">
            <h3>Current Status</h3>
            <div class="api-status">
                <div class="status-indicator" id="geminiStatus"></div>
                <span>Gemini API: <span id="geminiStatusText">Checking...</span></span>
            </div>
            <div class="api-status">
                <div class="status-indicator" id="openrouterStatus"></div>
                <span>OpenRouter API: <span id="openrouterStatusText">Checking...</span></span>
            </div>
        </div>

        <form id="setupForm">
            <div class="form-group">
                <label for="geminiKey">Gemini API Key:</label>
                <input type="password" id="geminiKey" name="geminiKey" placeholder="Enter your Gemini API key">
                <div class="help-text">
                    Get your API key from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>
                </div>
            </div>

            <div class="form-group">
                <label for="openrouterKey">OpenRouter API Key:</label>
                <input type="password" id="openrouterKey" name="openrouterKey" placeholder="Enter your OpenRouter API key">
                <div class="help-text">
                    Get your API key from <a href="https://openrouter.ai/keys" target="_blank">OpenRouter Dashboard</a>
                </div>
            </div>

            <button type="submit" class="button" id="saveButton">Save API Keys</button>
            <button type="button" class="button" onclick="google.script.host.close()">Cancel</button>
        </form>

        <div id="status" class="status"></div>
    </div>

    <script>
        // Check current API key status on load
        google.script.run
            .withSuccessHandler(updateStatus)
            .withFailureHandler(showError)
            .getApiKeyStatus();

        function updateStatus(status) {
            // Update Gemini status
            const geminiIndicator = document.getElementById('geminiStatus');
            const geminiText = document.getElementById('geminiStatusText');
            if (status.hasGemini) {
                geminiIndicator.className = 'status-indicator connected';
                geminiText.textContent = 'Connected';
            } else {
                geminiIndicator.className = 'status-indicator disconnected';
                geminiText.textContent = 'Not configured';
            }

            // Update OpenRouter status
            const openrouterIndicator = document.getElementById('openrouterStatus');
            const openrouterText = document.getElementById('openrouterStatusText');
            if (status.hasOpenRouter) {
                openrouterIndicator.className = 'status-indicator connected';
                openrouterText.textContent = 'Connected';
            } else {
                openrouterIndicator.className = 'status-indicator disconnected';
                openrouterText.textContent = 'Not configured';
            }
        }

        // Handle form submission
        document.getElementById('setupForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const geminiKey = document.getElementById('geminiKey').value.trim();
            const openrouterKey = document.getElementById('openrouterKey').value.trim();
            
            if (!geminiKey && !openrouterKey) {
                showStatus('Please enter at least one API key.', 'error');
                return;
            }

            // Disable button and show loading
            const saveButton = document.getElementById('saveButton');
            saveButton.disabled = true;
            saveButton.textContent = 'Saving...';

            // Save API keys
            google.script.run
                .withSuccessHandler(handleSaveSuccess)
                .withFailureHandler(handleSaveError)
                .saveApiKeys(geminiKey, openrouterKey);
        });

        function handleSaveSuccess(result) {
            const saveButton = document.getElementById('saveButton');
            saveButton.disabled = false;
            saveButton.textContent = 'Save API Keys';

            if (result.success) {
                showStatus(result.message, 'success');
                // Clear form
                document.getElementById('geminiKey').value = '';
                document.getElementById('openrouterKey').value = '';
                // Refresh status
                google.script.run
                    .withSuccessHandler(updateStatus)
                    .getApiKeyStatus();
            } else {
                showStatus(result.message, 'error');
            }
        }

        function handleSaveError(error) {
            const saveButton = document.getElementById('saveButton');
            saveButton.disabled = false;
            saveButton.textContent = 'Save API Keys';
            showStatus('Error saving API keys: ' + error.message, 'error');
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';
            
            // Hide after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }

        function showError(error) {
            showStatus('Error: ' + error.message, 'error');
        }
    </script>
</body>
</html>
