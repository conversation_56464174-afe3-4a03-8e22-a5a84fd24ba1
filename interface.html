<!DOCTYPE html>
<html>
<head>
    <base target="_top">
    <meta charset="utf-8">
    <title>AI Integration</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-height: 650px;
            overflow-y: auto;
        }
        h2 {
            color: #1a73e8;
            margin-bottom: 20px;
        }
        .tabs {
            display: flex;
            border-bottom: 2px solid #e0e0e0;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 14px;
            color: #666;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            color: #1a73e8;
            border-bottom-color: #1a73e8;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        select, input[type="text"], input[type="password"], input[type="number"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            resize: vertical;
            min-height: 80px;
        }
        .button {
            background-color: #1a73e8;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .button:hover {
            background-color: #1557b0;
        }
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .button.secondary {
            background-color: #6c757d;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .api-status {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.connected {
            background-color: #34a853;
        }
        .status-indicator.disconnected {
            background-color: #ea4335;
        }
        .range-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #1a73e8;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .batch-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
        }
        .batch-item h4 {
            margin-top: 0;
            color: #333;
        }
        .help-section {
            margin-bottom: 25px;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #1a73e8;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>AI Integration for Google Sheets</h2>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('setup')">Setup</button>
            <button class="tab" onclick="showTab('process')">Process</button>
            <button class="tab" onclick="showTab('batch')">Batch</button>
            <button class="tab" onclick="showTab('help')">Help</button>
        </div>

        <!-- Setup Tab -->
        <div id="setup" class="tab-content active">
            <h3>API Key Configuration</h3>
            
            <div id="currentStatus">
                <div class="api-status">
                    <div class="status-indicator" id="geminiStatus"></div>
                    <span>Gemini API: <span id="geminiStatusText">Checking...</span></span>
                </div>
                <div class="api-status">
                    <div class="status-indicator" id="openrouterStatus"></div>
                    <span>OpenRouter API: <span id="openrouterStatusText">Checking...</span></span>
                </div>
            </div>

            <form id="setupForm">
                <div class="form-group">
                    <label for="geminiKey">Gemini API Key:</label>
                    <input type="password" id="geminiKey" placeholder="Enter your Gemini API key">
                    <div class="help-text">
                        Get your API key from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>
                    </div>
                </div>

                <div class="form-group">
                    <label for="openrouterKey">OpenRouter API Key:</label>
                    <input type="password" id="openrouterKey" placeholder="Enter your OpenRouter API key">
                    <div class="help-text">
                        Get your API key from <a href="https://openrouter.ai/keys" target="_blank">OpenRouter Dashboard</a>
                    </div>
                </div>

                <button type="submit" class="button" id="saveButton">Save API Keys</button>
            </form>
        </div>

        <!-- Process Tab -->
        <div id="process" class="tab-content">
            <h3>Process Selected Range</h3>
            
            <div id="rangeInfo" class="range-info">
                <strong>Selected Range:</strong> <span id="rangeText">Loading...</span><br>
                <strong>Data Size:</strong> <span id="dataSize">Loading...</span>
            </div>

            <form id="processForm">
                <div class="form-group">
                    <label for="provider">AI Provider:</label>
                    <select id="provider" required>
                        <option value="">Select AI Provider</option>
                        <option value="gemini">Google Gemini</option>
                        <option value="openrouter">OpenRouter</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="model">Model:</label>
                    <select id="model" required>
                        <option value="">Select a provider first</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="prompt">Prompt:</label>
                    <textarea id="prompt" required placeholder="Enter your prompt here. The selected data will be automatically appended."></textarea>
                </div>

                <div class="form-group">
                    <label for="outputRange">Output Range (optional):</label>
                    <input type="text" id="outputRange" placeholder="e.g., D1 or D1:D10">
                    <div class="help-text">Where to write the AI response. Leave empty to show in dialog.</div>
                </div>

                <div class="form-group">
                    <label for="systemPrompt">System Prompt (optional):</label>
                    <textarea id="systemPrompt" placeholder="System instructions for the AI model"></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="temperature">Temperature:</label>
                        <input type="number" id="temperature" min="0" max="2" step="0.1" value="0.7">
                    </div>
                    <div class="form-group">
                        <label for="maxTokens">Max Tokens:</label>
                        <input type="number" id="maxTokens" min="1" max="4000" value="1000">
                    </div>
                </div>

                <button type="submit" class="button" id="processButton">Process with AI</button>
            </form>
        </div>

        <!-- Batch Tab -->
        <div id="batch" class="tab-content">
            <h3>Batch Processing</h3>
            
            <div class="form-group">
                <button type="button" class="button" onclick="addBatchItem()">Add Task</button>
                <button type="button" class="button secondary" onclick="clearBatch()">Clear All</button>
            </div>

            <div id="batchItems"></div>

            <button type="button" class="button" id="batchProcessButton" onclick="processBatch()">Process All Tasks</button>
        </div>

        <!-- Help Tab -->
        <div id="help" class="tab-content">
            <div class="help-section">
                <h3>🚀 Getting Started</h3>
                <ol>
                    <li><strong>Setup API Keys:</strong> Configure your Gemini and/or OpenRouter API keys</li>
                    <li><strong>Select Data:</strong> Highlight the cells you want to process in your sheet</li>
                    <li><strong>Process:</strong> Use the Process tab to send data to AI models</li>
                </ol>
            </div>

            <div class="help-section">
                <h3>💡 Example Use Cases</h3>
                <div class="code">
                    <strong>Data Analysis:</strong><br>
                    "Analyze this sales data and provide insights about trends and patterns."
                </div>
                <div class="code">
                    <strong>Text Summarization:</strong><br>
                    "Summarize each of these customer reviews in one sentence."
                </div>
                <div class="code">
                    <strong>Content Generation:</strong><br>
                    "Generate marketing copy for each of these products."
                </div>
            </div>

            <div class="help-section">
                <h3>🤖 Available Models</h3>
                <p><strong>Gemini:</strong> gemini-pro, gemini-pro-vision</p>
                <p><strong>OpenRouter:</strong> Claude 3, GPT-4, GPT-3.5, Llama 2, and more</p>
            </div>

            <div class="warning">
                <strong>Note:</strong> API keys are stored securely and your data is sent directly to the AI providers.
            </div>
        </div>

        <div id="status" class="status"></div>
        
        <div style="margin-top: 20px;">
            <button type="button" class="button secondary" onclick="google.script.host.close()">Close</button>
        </div>
    </div>

    <script>
        // Model options for each provider
        const modelOptions = {
            gemini: [
                { value: 'gemini-pro', text: 'Gemini Pro' },
                { value: 'gemini-pro-vision', text: 'Gemini Pro Vision' }
            ],
            openrouter: [
                { value: 'anthropic/claude-3-sonnet', text: 'Claude 3 Sonnet' },
                { value: 'anthropic/claude-3-haiku', text: 'Claude 3 Haiku' },
                { value: 'openai/gpt-4', text: 'GPT-4' },
                { value: 'openai/gpt-3.5-turbo', text: 'GPT-3.5 Turbo' },
                { value: 'meta-llama/llama-2-70b-chat', text: 'Llama 2 70B' }
            ]
        };

        let batchItemCount = 0;

        // Initialize on load
        window.onload = function() {
            checkApiStatus();
            loadRangeInfo();
        };

        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            // Load data for specific tabs
            if (tabName === 'process') {
                loadRangeInfo();
            }
        }

        // Check API key status
        function checkApiStatus() {
            google.script.run
                .withSuccessHandler(updateApiStatus)
                .withFailureHandler(showError)
                .getApiKeyStatus();
        }

        function updateApiStatus(status) {
            const geminiIndicator = document.getElementById('geminiStatus');
            const geminiText = document.getElementById('geminiStatusText');
            const openrouterIndicator = document.getElementById('openrouterStatus');
            const openrouterText = document.getElementById('openrouterStatusText');

            if (status.hasGemini) {
                geminiIndicator.className = 'status-indicator connected';
                geminiText.textContent = 'Connected';
            } else {
                geminiIndicator.className = 'status-indicator disconnected';
                geminiText.textContent = 'Not configured';
            }

            if (status.hasOpenRouter) {
                openrouterIndicator.className = 'status-indicator connected';
                openrouterText.textContent = 'Connected';
            } else {
                openrouterIndicator.className = 'status-indicator disconnected';
                openrouterText.textContent = 'Not configured';
            }
        }

        // Load range information
        function loadRangeInfo() {
            google.script.run
                .withSuccessHandler(updateRangeInfo)
                .withFailureHandler(showError)
                .getSelectedRange();
        }

        function updateRangeInfo(rangeInfo) {
            if (rangeInfo.success) {
                document.getElementById('rangeText').textContent = 
                    `${rangeInfo.sheetName}!${rangeInfo.range}`;
                document.getElementById('dataSize').textContent = 
                    `${rangeInfo.numRows} rows × ${rangeInfo.numCols} columns`;
            } else {
                document.getElementById('rangeText').textContent = 'Error loading range';
                document.getElementById('dataSize').textContent = rangeInfo.message;
            }
        }

        // Setup form handling
        document.getElementById('setupForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const geminiKey = document.getElementById('geminiKey').value.trim();
            const openrouterKey = document.getElementById('openrouterKey').value.trim();
            
            if (!geminiKey && !openrouterKey) {
                showStatus('Please enter at least one API key.', 'error');
                return;
            }

            const saveButton = document.getElementById('saveButton');
            saveButton.disabled = true;
            saveButton.textContent = 'Saving...';

            google.script.run
                .withSuccessHandler(handleSaveSuccess)
                .withFailureHandler(handleSaveError)
                .saveApiKeys(geminiKey, openrouterKey);
        });

        function handleSaveSuccess(result) {
            const saveButton = document.getElementById('saveButton');
            saveButton.disabled = false;
            saveButton.textContent = 'Save API Keys';

            if (result.success) {
                showStatus(result.message, 'success');
                document.getElementById('geminiKey').value = '';
                document.getElementById('openrouterKey').value = '';
                checkApiStatus();
            } else {
                showStatus(result.message, 'error');
            }
        }

        function handleSaveError(error) {
            const saveButton = document.getElementById('saveButton');
            saveButton.disabled = false;
            saveButton.textContent = 'Save API Keys';
            showStatus('Error saving API keys: ' + error.message, 'error');
        }

        // Provider selection handling
        document.getElementById('provider').addEventListener('change', function() {
            const provider = this.value;
            const modelSelect = document.getElementById('model');
            
            modelSelect.innerHTML = '<option value="">Select a model</option>';
            
            if (provider && modelOptions[provider]) {
                modelOptions[provider].forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.value;
                    option.textContent = model.text;
                    modelSelect.appendChild(option);
                });
            }
        });

        // Process form handling
        document.getElementById('processForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const provider = formData.get('provider');
            const model = formData.get('model');
            const prompt = formData.get('prompt');
            const systemPrompt = formData.get('systemPrompt');
            const temperature = parseFloat(formData.get('temperature'));
            const maxTokens = parseInt(formData.get('maxTokens'));
            const outputRange = formData.get('outputRange');

            if (!provider || !model || !prompt) {
                showStatus('Please fill in all required fields.', 'error');
                return;
            }

            const processButton = document.getElementById('processButton');
            processButton.disabled = true;
            processButton.textContent = 'Processing...';

            google.script.run
                .withSuccessHandler(handleProcessSuccess)
                .withFailureHandler(handleProcessError)
                .processAIRequest(provider, model, prompt, systemPrompt, temperature, maxTokens, outputRange);
        });

        function handleProcessSuccess(result) {
            const processButton = document.getElementById('processButton');
            processButton.disabled = false;
            processButton.textContent = 'Process with AI';

            if (result.success) {
                showStatus(result.message, 'success');
                
                if (!document.getElementById('outputRange').value) {
                    const responseWindow = window.open('', '_blank', 'width=600,height=400');
                    responseWindow.document.write(`
                        <html>
                        <head><title>AI Response</title></head>
                        <body style="font-family: Arial, sans-serif; padding: 20px;">
                        <h2>AI Response</h2>
                        <pre style="white-space: pre-wrap; background: #f5f5f5; padding: 15px; border-radius: 4px;">${result.response}</pre>
                        </body>
                        </html>
                    `);
                }
            } else {
                showStatus(result.message, 'error');
            }
        }

        function handleProcessError(error) {
            const processButton = document.getElementById('processButton');
            processButton.disabled = false;
            processButton.textContent = 'Process with AI';
            showStatus('Error processing request: ' + error.message, 'error');
        }

        // Batch processing functions
        function addBatchItem() {
            batchItemCount++;
            const itemId = `batch-item-${batchItemCount}`;
            
            const itemHtml = `
                <div class="batch-item" id="${itemId}">
                    <h4>Task ${batchItemCount} 
                        <button type="button" class="button danger" style="float: right; padding: 5px 10px; font-size: 12px;" onclick="removeBatchItem('${itemId}')">Remove</button>
                    </h4>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>Input Range:</label>
                            <input type="text" name="inputRange" placeholder="e.g., A1:B10" required>
                        </div>
                        <div class="form-group">
                            <label>Output Range:</label>
                            <input type="text" name="outputRange" placeholder="e.g., D1">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>AI Provider:</label>
                            <select name="provider" onchange="updateBatchModelOptions(this)" required>
                                <option value="">Select Provider</option>
                                <option value="gemini">Google Gemini</option>
                                <option value="openrouter">OpenRouter</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Model:</label>
                            <select name="model" required>
                                <option value="">Select provider first</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Prompt:</label>
                        <textarea name="prompt" placeholder="What should the AI do with this data?" required></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Temperature:</label>
                            <input type="number" name="temperature" min="0" max="2" step="0.1" value="0.7">
                        </div>
                        <div class="form-group">
                            <label>Max Tokens:</label>
                            <input type="number" name="maxTokens" min="1" max="4000" value="1000">
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('batchItems').insertAdjacentHTML('beforeend', itemHtml);
        }

        function removeBatchItem(itemId) {
            document.getElementById(itemId).remove();
            if (document.querySelectorAll('.batch-item').length === 0) {
                addBatchItem();
            }
        }

        function updateBatchModelOptions(providerSelect) {
            const provider = providerSelect.value;
            const batchItem = providerSelect.closest('.batch-item');
            const modelSelect = batchItem.querySelector('select[name="model"]');
            
            modelSelect.innerHTML = '<option value="">Select a model</option>';
            
            if (provider && modelOptions[provider]) {
                modelOptions[provider].forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.value;
                    option.textContent = model.text;
                    modelSelect.appendChild(option);
                });
            }
        }

        function clearBatch() {
            document.getElementById('batchItems').innerHTML = '';
            batchItemCount = 0;
            addBatchItem();
        }

        function processBatch() {
            const batchItems = document.querySelectorAll('.batch-item');
            const requests = [];
            
            for (let item of batchItems) {
                const inputRange = item.querySelector('input[name="inputRange"]').value.trim();
                const outputRange = item.querySelector('input[name="outputRange"]').value.trim();
                const provider = item.querySelector('select[name="provider"]').value;
                const model = item.querySelector('select[name="model"]').value;
                const prompt = item.querySelector('textarea[name="prompt"]').value.trim();
                const temperature = parseFloat(item.querySelector('input[name="temperature"]').value);
                const maxTokens = parseInt(item.querySelector('input[name="maxTokens"]').value);
                
                if (!inputRange || !provider || !model || !prompt) {
                    showStatus('Please fill in all required fields for all tasks.', 'error');
                    return;
                }
                
                requests.push({
                    inputRange, outputRange, provider, model, prompt, temperature, maxTokens
                });
            }
            
            if (requests.length === 0) {
                showStatus('No tasks to process.', 'error');
                return;
            }

            const processButton = document.getElementById('batchProcessButton');
            processButton.disabled = true;
            processButton.textContent = 'Processing...';

            google.script.run
                .withSuccessHandler(handleBatchSuccess)
                .withFailureHandler(handleBatchError)
                .batchProcess(requests);
        }

        function handleBatchSuccess(results) {
            const processButton = document.getElementById('batchProcessButton');
            processButton.disabled = false;
            processButton.textContent = 'Process All Tasks';
            
            const successCount = results.filter(r => r.success).length;
            const totalCount = results.length;
            
            showStatus(`Batch processing completed: ${successCount}/${totalCount} tasks successful`, 
                      successCount === totalCount ? 'success' : 'error');
        }

        function handleBatchError(error) {
            const processButton = document.getElementById('batchProcessButton');
            processButton.disabled = false;
            processButton.textContent = 'Process All Tasks';
            showStatus('Error processing batch: ' + error.message, 'error');
        }

        // Utility functions
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }

        function showError(error) {
            showStatus('Error: ' + error.message, 'error');
        }

        // Initialize batch with one item
        addBatchItem();
    </script>
</body>
</html>
