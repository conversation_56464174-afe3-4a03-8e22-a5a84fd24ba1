<!DOCTYPE html>
<html>
<head>
    <base target="_top">
    <meta charset="utf-8">
    <title>Batch AI Processing</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 {
            color: #1a73e8;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        select, input[type="text"], input[type="number"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            resize: vertical;
            min-height: 60px;
        }
        .button {
            background-color: #1a73e8;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .button:hover {
            background-color: #1557b0;
        }
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .button.secondary {
            background-color: #6c757d;
        }
        .button.secondary:hover {
            background-color: #545b62;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .button.danger:hover {
            background-color: #c82333;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .batch-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
        }
        .batch-item h4 {
            margin-top: 0;
            color: #333;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .results-container {
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: none;
        }
        .result-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .result-item:last-child {
            border-bottom: none;
        }
        .result-item.success {
            background-color: #d4edda;
        }
        .result-item.error {
            background-color: #f8d7da;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        .progress-fill {
            height: 100%;
            background-color: #1a73e8;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Batch AI Processing</h2>
        
        <div class="form-group">
            <button type="button" class="button" onclick="addBatchItem()">Add Processing Task</button>
            <button type="button" class="button secondary" onclick="clearAll()">Clear All</button>
        </div>

        <div id="batchItems">
            <!-- Batch items will be added here -->
        </div>

        <div class="form-group">
            <button type="button" class="button" id="processButton" onclick="processBatch()">Process All Tasks</button>
            <button type="button" class="button" onclick="google.script.host.close()">Cancel</button>
        </div>

        <div class="progress-bar" id="progressBar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div id="status" class="status"></div>

        <div id="resultsContainer" class="results-container">
            <h3>Processing Results</h3>
            <div id="results"></div>
        </div>
    </div>

    <script>
        let batchItemCount = 0;
        
        // Model options for each provider
        const modelOptions = {
            gemini: [
                { value: 'gemini-pro', text: 'Gemini Pro' },
                { value: 'gemini-pro-vision', text: 'Gemini Pro Vision' }
            ],
            openrouter: [
                { value: 'anthropic/claude-3-sonnet', text: 'Claude 3 Sonnet' },
                { value: 'anthropic/claude-3-haiku', text: 'Claude 3 Haiku' },
                { value: 'openai/gpt-4', text: 'GPT-4' },
                { value: 'openai/gpt-3.5-turbo', text: 'GPT-3.5 Turbo' },
                { value: 'meta-llama/llama-2-70b-chat', text: 'Llama 2 70B' }
            ]
        };

        // Add initial batch item
        addBatchItem();

        function addBatchItem() {
            batchItemCount++;
            const itemId = `batch-item-${batchItemCount}`;
            
            const itemHtml = `
                <div class="batch-item" id="${itemId}">
                    <h4>Task ${batchItemCount} 
                        <button type="button" class="button danger" style="float: right; padding: 5px 10px; font-size: 12px;" onclick="removeBatchItem('${itemId}')">Remove</button>
                    </h4>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>Input Range:</label>
                            <input type="text" name="inputRange" placeholder="e.g., A1:B10" required>
                            <div class="help-text">Range containing data to process</div>
                        </div>
                        <div class="form-group">
                            <label>Output Range:</label>
                            <input type="text" name="outputRange" placeholder="e.g., D1">
                            <div class="help-text">Where to write AI response</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>AI Provider:</label>
                            <select name="provider" onchange="updateModelOptions(this)" required>
                                <option value="">Select Provider</option>
                                <option value="gemini">Google Gemini</option>
                                <option value="openrouter">OpenRouter</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Model:</label>
                            <select name="model" required>
                                <option value="">Select provider first</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Prompt:</label>
                        <textarea name="prompt" placeholder="What should the AI do with this data?" required></textarea>
                    </div>

                    <div class="form-group">
                        <label>System Prompt (optional):</label>
                        <textarea name="systemPrompt" placeholder="Additional context or instructions"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Temperature:</label>
                            <input type="number" name="temperature" min="0" max="2" step="0.1" value="0.7">
                        </div>
                        <div class="form-group">
                            <label>Max Tokens:</label>
                            <input type="number" name="maxTokens" min="1" max="4000" value="1000">
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('batchItems').insertAdjacentHTML('beforeend', itemHtml);
        }

        function removeBatchItem(itemId) {
            const item = document.getElementById(itemId);
            if (item) {
                item.remove();
            }
            
            // If no items left, add one
            if (document.querySelectorAll('.batch-item').length === 0) {
                addBatchItem();
            }
        }

        function updateModelOptions(providerSelect) {
            const provider = providerSelect.value;
            const batchItem = providerSelect.closest('.batch-item');
            const modelSelect = batchItem.querySelector('select[name="model"]');
            
            // Clear existing options
            modelSelect.innerHTML = '<option value="">Select a model</option>';
            
            if (provider && modelOptions[provider]) {
                modelOptions[provider].forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.value;
                    option.textContent = model.text;
                    modelSelect.appendChild(option);
                });
            }
        }

        function clearAll() {
            document.getElementById('batchItems').innerHTML = '';
            batchItemCount = 0;
            addBatchItem();
            hideResults();
        }

        function processBatch() {
            const batchItems = document.querySelectorAll('.batch-item');
            const requests = [];
            
            // Validate and collect all requests
            for (let item of batchItems) {
                const inputRange = item.querySelector('input[name="inputRange"]').value.trim();
                const outputRange = item.querySelector('input[name="outputRange"]').value.trim();
                const provider = item.querySelector('select[name="provider"]').value;
                const model = item.querySelector('select[name="model"]').value;
                const prompt = item.querySelector('textarea[name="prompt"]').value.trim();
                const systemPrompt = item.querySelector('textarea[name="systemPrompt"]').value.trim();
                const temperature = parseFloat(item.querySelector('input[name="temperature"]').value);
                const maxTokens = parseInt(item.querySelector('input[name="maxTokens"]').value);
                
                if (!inputRange || !provider || !model || !prompt) {
                    showStatus('Please fill in all required fields for all tasks.', 'error');
                    return;
                }
                
                requests.push({
                    inputRange,
                    outputRange,
                    provider,
                    model,
                    prompt,
                    systemPrompt,
                    temperature,
                    maxTokens
                });
            }
            
            if (requests.length === 0) {
                showStatus('No tasks to process.', 'error');
                return;
            }

            // Disable button and show progress
            const processButton = document.getElementById('processButton');
            processButton.disabled = true;
            processButton.textContent = 'Processing...';
            
            showProgress(0);
            clearResults();

            // Process batch
            google.script.run
                .withSuccessHandler(handleBatchSuccess)
                .withFailureHandler(handleBatchError)
                .batchProcess(requests);
        }

        function handleBatchSuccess(results) {
            const processButton = document.getElementById('processButton');
            processButton.disabled = false;
            processButton.textContent = 'Process All Tasks';
            
            hideProgress();
            showResults(results);
            
            const successCount = results.filter(r => r.success).length;
            const totalCount = results.length;
            
            showStatus(`Batch processing completed: ${successCount}/${totalCount} tasks successful`, 
                      successCount === totalCount ? 'success' : 'error');
        }

        function handleBatchError(error) {
            const processButton = document.getElementById('processButton');
            processButton.disabled = false;
            processButton.textContent = 'Process All Tasks';
            
            hideProgress();
            showStatus('Error processing batch: ' + error.message, 'error');
        }

        function showProgress(percent) {
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            progressBar.style.display = 'block';
            progressFill.style.width = percent + '%';
        }

        function hideProgress() {
            document.getElementById('progressBar').style.display = 'none';
        }

        function showResults(results) {
            const resultsContainer = document.getElementById('resultsContainer');
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = '';
            
            results.forEach((result, index) => {
                const resultItem = document.createElement('div');
                resultItem.className = `result-item ${result.success ? 'success' : 'error'}`;
                
                if (result.success) {
                    resultItem.innerHTML = `
                        <strong>Task ${index + 1}: Success</strong><br>
                        Input: ${result.inputRange}<br>
                        Output: ${result.outputRange}<br>
                        Response: ${result.response}
                    `;
                } else {
                    resultItem.innerHTML = `
                        <strong>Task ${index + 1}: Error</strong><br>
                        Input: ${result.inputRange}<br>
                        Error: ${result.error}
                    `;
                }
                
                resultsDiv.appendChild(resultItem);
            });
            
            resultsContainer.style.display = 'block';
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function hideResults() {
            document.getElementById('resultsContainer').style.display = 'none';
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';
            
            // Hide after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }
    </script>
</body>
</html>
